/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react'
import {
  CButton,
  CAccordion,
  CAccordionItem,
  CAccordionHeader,
  CAccordionBody,
  CModal,
  CModalHeader,
  CModalBody,
  CForm,
  CFormLabel,
  CFormInput,
  CSpinner,
  CImage,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPencil, cilTrash } from '@coreui/icons'
import Joi from 'joi'
import { useForm } from 'react-hook-form'
import EmptyBox from 'src/components/EmptyBox'
import { addDoc, collection, getDocs, updateDoc, doc, deleteDoc } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'
import Swal from 'sweetalert2'
import { joiResolver } from '@hookform/resolvers/joi'
import { toast } from 'react-toastify'

const FAQS = () => {
  const [FAQs, setFAQs] = useState([])
  const [isLoading, setLoading] = useState(true)
  const [refresh, setRefresh] = useState(0)
  const [editFAQ, setEditFAQ] = useState(null)

  // MODAL HANDLERS - ADD FAQ MODAL
  const [showFAQ, setShowFAQ] = useState(false)
  const handleShowFAQ = () => setShowFAQ(true)
  const handleCloseFAQ = () => {
    setShowFAQ(false)
    setEditFAQ(null)
  }

  // GET ALL FAQS
  async function getAllFAQs() {
    setLoading(true)
    const arrOfFAQs = []
    const FAQsDocs = await getDocs(collection(db, 'FAQs'))
    FAQsDocs.forEach((FAQ) => {
      arrOfFAQs.push({ id: FAQ.id, ...FAQ.data() })
    })
    setFAQs(arrOfFAQs)
    setLoading(false)
  }

  // HANDLE EDIT BUTTON
  const handleEdit = (faq) => {
    setEditFAQ(faq)
    handleShowFAQ()
  }

  // DELETE CATEGORY AND IMAGE
  const deleteThisFAQ = async (faq) => {
    Swal.fire({
      title: 'Delete?',
      text: 'Are you sure to delete this FAQ?',
      icon: 'question',
      confirmButtonColor: '#e55353',
      confirmButtonText: 'Confirm',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        setLoading(true)
        await deleteDoc(doc(db, 'FAQs', faq?.id))
          .then(async () => {
            Swal.fire({
              title: 'Deleted',
              text: 'FAQ deleted successfully',
              icon: 'success',
            })
            setRefresh((n) => n + 1)
          })
          .catch((error) => {
            setLoading(false)
            toast.error('Something went wrong while deleting an FAQ.')
          })
      }
    })
  }

  // GET ALL FAQS
  useEffect(() => {
    getAllFAQs()
  }, [refresh])

  // LOADING
  if (isLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )
  return (
    <>
      <div className="d-flex justify-content-between mb-2">
        <h4>FAQs</h4>
        <CButton onClick={handleShowFAQ}>Add FAQ</CButton>
      </div>
      <CAccordion activeItemKey={0}>
        {FAQs.length ? (
          FAQs.map((item, i) => {
            return (
              <CAccordionItem itemKey={i + 1} key={i}>
                <CAccordionHeader>
                  {item?.question}
                  <div className="ms-auto">
                    <span
                      className="btn btn-primary btn-sm"
                      onClick={(event) => {
                        event.stopPropagation()
                        handleEdit(item)
                      }}
                    >
                      <CIcon icon={cilPencil} />
                    </span>
                    <span
                      className="btn btn-danger btn-sm ms-2"
                      onClick={(event) => {
                        event.stopPropagation()
                        deleteThisFAQ(item)
                      }}
                    >
                      <CIcon icon={cilTrash} className="white-icon" />
                    </span>
                  </div>
                </CAccordionHeader>
                <CAccordionBody>{item?.answer}</CAccordionBody>
              </CAccordionItem>
            )
          })
        ) : (
          <EmptyBox label={'No FAQs Data..'} />
        )}
      </CAccordion>

      {/* ADD FAQ MODAL */}
      <AddFAQModal
        visible={showFAQ}
        faq={editFAQ}
        closeModal={handleCloseFAQ}
        setRefresh={setRefresh}
      />
    </>
  )
}

export default FAQS

const AddFAQModal = ({ visible, closeModal, setRefresh, faq }) => {
  const [isLoading, setLoading] = useState(false)
  const faqSchema = Joi.object({
    question: Joi.string().required().min(3).messages({
      'string.base': 'Question is required.',
      'string.empty': 'Question is required.',
      'string.required': 'Question is required.',
      'string.min': 'Question must have 3 letters.',
    }),
    answer: Joi.string().required().min(3).messages({
      'string.base': 'Answer is required.',
      'string.empty': 'Answer is required.',
      'string.required': 'Answer is required.',
      'string.min': 'Answer must have 3 letters.',
    }),
  })

  const {
    handleSubmit,
    register,
    formState: { errors },
    setValue,
    reset,
  } = useForm({
    defaultValues: {
      question: '',
      answer: '',
    },
    mode: 'onBlur',
    resolver: joiResolver(faqSchema),
  })

  // ADD NEW FAQ
  async function addNewFAQ(payload) {
    setLoading(true)
    // ADD NEW FAQ
    if (!faq) {
      await addDoc(collection(db, 'FAQs'), payload)
        .then(() => {
          setRefresh((n) => n + 1)
          closeModal()
        })
        .catch((error) => {
          setLoading(false)
          toast.error('Something went wrong while adding new FAQ.')
        })
    }
    // UPDATE EXISTING FAQ
    if (faq) {
      await updateDoc(doc(db, 'FAQs', faq?.id), payload)
        .then(() => {
          setRefresh((n) => n + 1)
          closeModal()
        })
        .catch((error) => {
          setLoading(false)
          toast.error('Something went wrong while updating an FAQ.')
        })
    }
  }

  // SET VALUE TO FORM FOR EDIT
  useEffect(() => {
    if (faq) {
      setValue('question', faq?.question)
      setValue('answer', faq?.answer)
    }
  }, [faq])
  return (
    <>
      <CModal
        visible={visible}
        onClose={() => {
          closeModal()
          reset()
        }}
        size="lg"
      >
        <CModalHeader closeButton>{faq ? 'Edit FAQ' : 'Add New FAQ'}</CModalHeader>
        <CModalBody>
          {/* FORM */}
          <CForm onSubmit={handleSubmit(addNewFAQ)}>
            {/* QUESTION */}
            <CFormLabel htmlFor="question">Question</CFormLabel>
            <CFormInput type="text" {...register('question')} id="question" />
            {errors?.question ? (
              <p className="m-0 text-danger">{errors?.question?.message}</p>
            ) : null}

            {/* ANSWER */}
            <CFormLabel htmlFor="question" className="mt-2">
              Answer
            </CFormLabel>
            <CFormInput type="text" {...register('answer')} id="question" />
            {errors?.answer ? <p className="m-0 text-danger">{errors?.answer?.message}</p> : null}

            {/* SUBMIT BUTTON */}
            <div className="mt-3 text-end">
              <CButton type="submit" disabled={isLoading}>
                Submit
                {isLoading ? <CSpinner size="sm" className="ms-2" /> : null}
              </CButton>
            </div>
          </CForm>
        </CModalBody>
      </CModal>
    </>
  )
}
