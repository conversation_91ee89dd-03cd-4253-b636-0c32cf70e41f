import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getStorage, ref } from "firebase/storage";
import { getFirestore } from "firebase/firestore";

const firebaseConfig = {
  apiKey: "AIzaSyBzzpvJEzyEPNZW3bW2esl6wjTBVbPTuKo",
  authDomain: "glitterofhope.firebaseapp.com",
  projectId: "glitterofhope",
  storageBucket: "glitterofhope.appspot.com",
  messagingSenderId: "112886832187",
  appId: "1:112886832187:web:1236147aaa1e285827901e",
  measurementId: "G-GP85FGXK57",
};

export const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const storage = getStorage(app);
export const storageRef = ref(storage);
export const db = getFirestore(app);
