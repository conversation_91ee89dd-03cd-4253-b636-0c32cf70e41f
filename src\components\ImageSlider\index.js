/* eslint-disable react/prop-types */
import React from 'react'
import { CCarousel, CCarouselItem, CImage } from '@coreui/react'
const ImageSlider = ({ images }) => {
  return (
    <>
      <CCarousel controls indicators interval={false}>
        {images?.map((url, i) => {
          return (
            <CCarouselItem key={i}>
              <CImage
                className="d-block w-100"
                src={url}
                alt="slide 1"
                style={{ maxHeight: '15rem', objectFit: 'contain', maxWidth: 'auto' }}
              />
            </CCarouselItem>
          )
        })}
      </CCarousel>
    </>
  )
}

export default ImageSlider
