/* eslint-disable react/prop-types */
import React from 'react'
import { CBadge } from '@coreui/react'
// import CIcon from '@coreui/icons-react'
// import { cilPencil } from '@coreui/icons'
const Status = ({ status, approveThisPost }) => {
  // PENDING
  if (status === 'pending')
    return (
      <h5 className="position-relative me-2">
        <CBadge color="warning">Pending</CBadge>
        {/* <span
          className="position-absolute bg-primary d-flex justify-content-center align-items-center rounded-circle cursor-pointer"
          style={{ width: 20, height: 20, top: -8, right: -8 }}
          onClick={() => approveThisPost()}
        >
          <CIcon icon={cilPencil} height={10} width={10} className="white-icon" />
        </span> */}
      </h5>
    )
  // ACTIVE
  if (status === 'approved')
    return (
      <h5>
        <CBadge color="success">Approved</CBadge>
      </h5>
    )
  // HISTORY
  if (status === 'history')
    return (
      <h5>
        <CBadge color="primary">History</CBadge>
      </h5>
    )
}

export default Status
