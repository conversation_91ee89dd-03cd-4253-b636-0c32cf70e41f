{"name": "glitter-of-hope", "version": "4.5.0", "description": "CoreUI Free React Admin Template", "homepage": ".", "bugs": {"url": "https://github.com/coreui/coreui-free-react-admin-template/issues"}, "repository": {"type": "git", "url": "**************:coreui/coreui-free-react-admin-template.git"}, "license": "MIT", "author": "The CoreUI Team (https://github.com/orgs/coreui/people)", "scripts": {"build": "react-scripts build", "eject": "react-scripts eject", "lint": "eslint \"src/**/*.js\"", "start": "react-scripts start", "test": "react-scripts test", "test:cov": "npm test -- --coverage --watchAll=false", "test:debug": "react-scripts --inspect-brk test --runInBand"}, "dependencies": {"@coreui/chartjs": "^3.1.1", "@coreui/coreui": "^4.2.6", "@coreui/icons": "^3.0.1", "@coreui/icons-react": "^2.1.0", "@coreui/react": "^4.6.0", "@coreui/react-chartjs": "^2.1.2", "@coreui/utils": "^2.0.1", "@hookform/resolvers": "^3.3.2", "chart.js": "^3.9.1", "classnames": "^2.3.2", "core-js": "^3.29.0", "firebase": "^10.5.0", "joi": "^17.11.0", "moment": "^2.30.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-app-polyfill": "^3.0.0", "react-chat-engine": "^1.11.28", "react-custom-scrollbars": "^4.2.1", "react-datepicker": "^4.10.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-icons": "^5.2.1", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-responsive-datepicker": "^1.0.6", "react-router-dom": "^6.8.2", "react-rte": "^0.16.5", "react-table": "^7.8.0", "react-toastify": "^9.1.3", "redux": "4.2.1", "simplebar-react": "^2.4.3", "sweetalert2": "^11.7.32"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "2.8.4", "react-scripts": "5.0.1", "sass": "^1.58.3", "web-vitals": "^3.1.1"}, "engines": {"node": ">=10", "npm": ">=6"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}