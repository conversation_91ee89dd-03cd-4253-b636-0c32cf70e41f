import React from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CForm,
  CFormInput,
  CFormLabel,
  CFormTextarea,
  CRow,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilSend } from '@coreui/icons'
import { ChatEngine } from 'react-chat-engine'

const Chat = () => {
  return (
    <ChatEngine
      publicKey={'8d5ac088-e5de-4f5e-be90-1214a3082c72'}
      userName={'Faisu'}
      userSecret={'Test@123'}
    />
  )
}

export default Chat
