import React, { useState, useEffect } from "react";
import {
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CFormInput,
  CSpinner,
  CContainer,
  CImage,
  CForm,
  CFormSelect,
} from "@coreui/react";
import {
  getDocs,
  collection,
  doc,
  deleteDoc,
  setDoc,
  updateDoc,
  getDoc,
} from "firebase/firestore";
import { db, storage } from "src/firebase/firebase";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import Swal from "sweetalert2";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { FaEye } from "react-icons/fa";
import { uploadThisImage } from "src/utils/FirebaseUtils";
import { useForm } from "react-hook-form";
import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import Joi from "joi";
import moment from "moment";
import EmptyBox from "src/components/EmptyBox";

const schema = Joi.object({
  title: Joi.string().required().label("Title"),
  from: Joi.string().required().label("From"),
  about: Joi.string().required().label("About"),
  target: Joi.number().required().label("Target"),
  urgent: Joi.boolean().required().label("urgent"),
  done: Joi.number().required().label("Done"),
  picture: Joi.any().label("Picture"),
  endDate: Joi.date().required().label("End Date"),
});

const Projects = () => {
  const [projects, setProjects] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [modalData, setModalData] = useState({});
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
  } = useForm({
    resolver: joiResolver(schema),
    defaultValues: {
      title: "",
      from: "",
      about: "",
      target: "",
      done: "",
      urgent: "",
      picture: null,
      endDate: new Date(),
    },
  });

  const [selectedProjectId, setSelectedProjectId] = useState(null);

  const fetchProjects = async () => {
    setLoading(true);
    const projectsCollection = collection(db, "Projects");
    const projectsSnapshot = await getDocs(projectsCollection);
    const projectsList = projectsSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
    setProjects(projectsList);
    setLoading(false);
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  // const handleOpenDetailModal = (project) => {
  //   setModalData({ ...project, endDate: project.endDate?.toDate() });
  //   setShowDetailModal(true);
  // };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setModalData((prevState) => ({ ...prevState, [name]: value }));
  };

  const handleDateChange = (date) => {
    setModalData((prevState) => ({
      ...prevState,
      endDate: moment(date).toDate(),
    }));
  };
  const handleOpenDetailModal = (project) => {
    setModalData({
      ...project,
      endDate: new Date(project.endDate.seconds * 1000),
    });
    setShowDetailModal(true);
  };
  const convertToDataURL = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
      reader.readAsDataURL(file);
    });
  };
  const handleSaveProject = async (values) => {
    try {
      const { title, from, about, target, done, picture, endDate, urgent } =
        values;

      let pictureUrl = "";

      if (editMode && selectedProjectId) {
        const projectDoc = await getDoc(doc(db, "Projects", selectedProjectId));
        const existingProject = projectDoc.data();

        if (picture && picture !== existingProject.picture) {
          pictureUrl = await uploadThisImage(picture, "Projects");
        } else {
          pictureUrl = existingProject.picture;
        }
      } else {
        if (picture) {
          pictureUrl = await uploadThisImage(picture, "Projects");
        }
      }

      const projectData = {
        title,
        from,
        about,
        target: parseInt(target),
        done: parseInt(done),
        picture: pictureUrl,
        endDate: moment(endDate).toDate(),
        updatedAt: new Date(),
        urgent,
      };

      if (editMode && selectedProjectId) {
        await updateDoc(doc(db, "Projects", selectedProjectId), projectData, {
          merge: true,
        });
      } else {
        projectData.createdAt = new Date();
        await setDoc(
          doc(db, "Projects", new Date().getTime().toString()),
          projectData
        );
      }

      Swal.fire(
        "Success",
        `Project ${editMode ? "updated" : "created"} successfully!`,
        "success"
      );
      fetchProjects();
      handleCloseModal();
    } catch (error) {
      console.error("Error saving project:", error);
      Swal.fire("Error", "There was an issue saving the project.", "error");
    }
  };
  const handleCloseModal = () => {
    reset();
    setShowModal(false);
    setModalData({}); // Clear modal data
    setEditMode(false);
    setSelectedProjectId(null);
  };

  const handleDeleteProject = async (id) => {
    const result = await Swal.fire({
      title: "Delete?",
      text: "Are you sure to delete this project?",
      icon: "question",
      confirmButtonColor: "#e55353",
      confirmButtonText: "Confirm",
      cancelButtonColor: "#9da5b1",
      showCancelButton: true,
    });
    if (result.isConfirmed) {
      try {
        await deleteDoc(doc(db, "Projects", id));
        fetchProjects();
        Swal.fire("Deleted!", "Project has been deleted.", "success");
      } catch (error) {
        console.error("Error deleting project:", error);
        Swal.fire("Error", "There was an issue deleting the project.", "error");
      }
    }
  };

  // HANDLE CHANGE IMAGE OF FORM
  const handleChangeImage = async (event) => {
    const file = event?.target?.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target.result;
        setValue("picture", dataUrl);
      };
      reader.readAsDataURL(file);
    }
  };
  const handleOpenModal = (project = {}) => {
    setEditMode(!!project.id);
    setModalData(project); // Set modal data to project data or empty for new
    setValue("title", project.title || "");
    setValue("from", project.from || "");
    setValue("about", project.about || "");
    setValue("target", project.target || "");
    setValue("done", project.done);
    setValue("picture", project.picture || "");
    setValue("urgent", project.urgent || false);
    setSelectedProjectId(project.id);
    setValue(
      "endDate",
      project.endDate
        ? moment(project.endDate.seconds * 1000).format("yyyy-MM-DD")
        : ""
    );
    setShowModal(true);
  };

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    );
  }

  return (
    <CContainer>
      <h4>Projects</h4>
      <CButton
        color="primary"
        className="mb-3"
        onClick={() => handleOpenModal()}
      >
        Add New Project
      </CButton>

      {projects.length ? (
        <CTable responsive>
          <CTableHead>
            <CTableRow>
              <CTableHeaderCell>#</CTableHeaderCell>
              <CTableHeaderCell>Image</CTableHeaderCell>
              <CTableHeaderCell>Title</CTableHeaderCell>
              <CTableHeaderCell>From</CTableHeaderCell>

              <CTableHeaderCell>End Date</CTableHeaderCell>
              <CTableHeaderCell>Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {projects.map((project, index) => (
              <CTableRow key={project.id}>
                <CTableDataCell>{index + 1}</CTableDataCell>
                <CTableDataCell>
                  <CImage
                    width={40}
                    height={40}
                    className="object-cover rounded"
                    src={project.picture || ""}
                  />
                </CTableDataCell>
                <CTableDataCell>{project.title}</CTableDataCell>
                <CTableDataCell>{project.from}</CTableDataCell>

                <CTableDataCell>
                  {moment(new Date(project.endDate.seconds * 1000)).format(
                    "DD-MMM-YYYY"
                  )}
                </CTableDataCell>
                <CTableDataCell>
                  <CButton
                    size="sm"
                    color="info text-white"
                    onClick={() => handleOpenModal(project)}
                  >
                    Edit
                  </CButton>
                  <CButton
                    size="sm"
                    color="danger text-white ms-2"
                    onClick={() => handleDeleteProject(project.id)}
                  >
                    Delete
                  </CButton>
                  <CButton
                    size="sm"
                    color="secondary text-white ms-2"
                    onClick={() => handleOpenDetailModal(project)}
                  >
                    <FaEye />
                  </CButton>
                </CTableDataCell>
              </CTableRow>
            ))}
          </CTableBody>
        </CTable>
      ) : (
        <EmptyBox />
      )}

      {/* MODAL FOR CREATING/EDITING PROJECT */}
      <CModal visible={showModal} onClose={() => setShowModal(false)}>
        <CModalHeader>
          <CModalTitle>
            {editMode ? "Edit Project" : "Add New Project"}
          </CModalTitle>
        </CModalHeader>
        <CForm onSubmit={handleSubmit(handleSaveProject)}>
          <CModalBody>
            <CFormInput
              label="Title"
              name="title"
              {...register("title")}
              className="mb-3"
            />
            <CFormInput
              label="From"
              name="from"
              {...register("from")}
              className="mb-3"
            />
            <CFormInput
              label="About"
              name="about"
              {...register("about")}
              className="mb-3"
            />
            <CFormInput
              label="Target/Month"
              name="target"
              {...register("target")}
              type="number"
              className="mb-3"
            />
            <CFormInput
              label="Amount Done"
              name="done"
              {...register("done")}
              type="number"
              className="mb-3"
            />
            <CFormSelect
              label="Urgent"
              aria-label="Urgent"
              className="mb-3"
              options={[
                { label: "Select if this project is urgent" },
                { label: "True", value: true },
                { label: "False", value: false },
              ]}
              {...register("urgent")}
            />
            <CFormInput
              label="End Date"
              name="endDate"
              {...register("endDate")}
              type="date"
              className="mb-3 "
            />

            <CFormInput
              label="Picture"
              type="file"
              accept="image/*"
              name="picture"
              onChange={handleChangeImage}
              className="mb-3"
            />

            {watch("picture") ? (
              <>
                <div className="d-flex">
                  <img
                    src={watch("picture")}
                    className="rounded border mt-2 mx-auto"
                    style={{ height: "8rem", objectFit: "contain" }}
                  />
                </div>
              </>
            ) : null}
          </CModalBody>
          <CModalFooter>
            <CButton
              color="secondary"
              onClick={() => setShowModal(false)}
              type="button"
            >
              Cancel
            </CButton>
            <CButton color="primary" type="submit">
              {editMode ? "Update" : "Create"}
            </CButton>
          </CModalFooter>
        </CForm>
      </CModal>

      {/* PROJECT DETAILS MODAL */}
      <CModal
        visible={showDetailModal}
        onClose={() => setShowDetailModal(false)}
      >
        <CModalHeader>
          <CModalTitle>Project Details</CModalTitle>
        </CModalHeader>
        <CModalBody>
          <p>
            <strong>Title:</strong> {modalData.title}
          </p>
          <p>
            <strong>From:</strong> {modalData.from}
          </p>
          <p>
            <strong>About:</strong> {modalData.about}
          </p>
          <p>
            <strong>Target/Month:</strong> {modalData.target}
          </p>
          <p>
            <strong>Amount Done:</strong> {modalData.done}
          </p>
          <p>
            <strong>Urgent:</strong> {modalData.urgent ? "True" : "False"}
          </p>
          <p>
            <strong>End Date:</strong>{" "}
            {moment(modalData.endDate).format("DD-MMM-YYYY")}
          </p>
          {modalData.picture && (
            <CImage
              width={100}
              height={100}
              src={modalData.picture}
              className="object-cover rounded mt-3"
            />
          )}
        </CModalBody>
        <CModalFooter>
          <CButton color="secondary" onClick={() => setShowDetailModal(false)}>
            Close
          </CButton>
        </CModalFooter>
      </CModal>
    </CContainer>
  );
};

export default Projects;
