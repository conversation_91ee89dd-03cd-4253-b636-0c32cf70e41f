/* eslint-disable react/prop-types */
import React, { useState } from 'react'
import {
  CButton,
  CForm,
  CFormLabel,
  CFormInput,
  CFormTextarea,
  CSpinner,
  CImage,
  CCard,
  CCardHeader,
  CCardBody,
  CAlert,
} from '@coreui/react'
import Joi from 'joi'
import { useForm } from 'react-hook-form'
import { addDoc, collection, getDocs, query, where } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'
import Swal from 'sweetalert2'
import { joiResolver } from '@hookform/resolvers/joi'
import { toast } from 'react-toastify'
import Logo from '../../../assets/images/logo.png'

const reqSchema = Joi.object({
  name: Joi.string().empty().required().messages({
    'string.empty': 'Name is required.',
    'string.required': 'Name is required.',
  }),
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .messages({
      'string.empty': 'Email is required.',
      'string.required': 'Email is required.',
      'string.email': 'Email must be valid.',
    }),
  comment: Joi.string().required().messages({
    'string.empty': 'Comment is required.',
    'string.required': 'Comment is required.',
  }),
})

const DelAccountReq = () => {
  const [isLoading, setLoading] = useState(false)
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: '',
      email: '',
      comment: '',
    },
    resolver: joiResolver(reqSchema),
    mode: 'onSubmit',
  })

  async function submitForm(formData) {
    // Show confirmation dialog before proceeding
    const result = await Swal.fire({
      title: 'Confirm Account Deletion',
      text: 'Are you sure you want to delete your account and data? After deletion, we will not be able to recover this information.',
      icon: 'warning',
      confirmButtonColor: '#e55353',
      confirmButtonText: 'Delete Account',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    })

    // Only proceed if user confirmed
    if (!result.isConfirmed) {
      return
    }

    setLoading(true)
    let q = query(collection(db, 'Users'), where('email', '==', formData?.email))
    await getDocs(q)
      .then(async (response) => {
        // USER FOUND AS REGISTERED
        if (response.docs.length) {
          // IF USER ALREADY SUBMITTED THE REQUEST
          let q2 = query(collection(db, 'DeleteRequest'), where('email', '==', formData?.email))
          await getDocs(q2).then((resp) => {
            if (resp.docs.length) {
              setLoading(false)
              reset()
              return Swal.fire({
                title: 'Request Already Sent',
                text: 'Your request to delete the account is already submitted.',
              })
            }
          })

          // NEW REQUEST
          await addDoc(collection(db, 'DeleteRequest'), {
            userId: response.docs[0]?.id,
            firstName: response.docs[0]?.data()?.name,
            email: response.docs[0]?.data()?.email,
            comment: formData?.comment,
            status: 'pending',
            createdAt: new Date(),
          }).then(() => {
            Swal.fire({
              title: 'Request Sent',
              text: 'Your request to delete the account has been sent.',
            })
          })
          setLoading(false)
          reset()
        }
        // USER NOT FOUND
        if (response.docs.length === 0) {
          toast.error('Email not found.')
          setLoading(false)
        }
      })
      .catch((error) => {
        toast.error('Something went wrong while sending request.')
        console.log('ERROR -', error)
        setLoading(false)
      })
  }
  return (
    <>
      <div className="vw-100 vh-100 d-flex justify-content-center align-items-center mt-5 mb-5">
        <CCard className="req-card mt-5">
          <CCardHeader>
            <p className="mx-auto my-0 " >
              <CImage src={Logo} className="mx-auto" height={100} style={{display:'flex'}}/>
            </p>
          </CCardHeader>
          <CCardBody>
            <CAlert color="info" className="mb-4">
              You can delete your account from the mobile app in the profile section, or request us to delete your account here.
            </CAlert>
            <CForm onSubmit={handleSubmit(submitForm)}>
              <div className="mb-3">
                <CFormLabel htmlFor="name">Name</CFormLabel>
                <CFormInput type="text" id="name" {...register('name')} />
                {errors?.name ? <p className="mb-0 text-danger">{errors?.name?.message}</p> : null}
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="email">Email</CFormLabel>
                <CFormInput type="email" id="email" {...register('email')} />
                {errors?.email ? <p className="text-danger">{errors?.email.message}</p> : null}
              </div>
              <div className="mb-3">
                <CFormLabel htmlFor="comment">Comment</CFormLabel>
                <CFormTextarea
                  {...register('comment')}
                  id="comment"
                  rows={4}
                  placeholder="Why are you deleting your account permanently?"
                />
                {errors?.comment ? <p className="text-danger">{errors?.comment.message}</p> : null}
              </div>
              <div className="text-end">
                <CButton type="submit" disabled={isLoading}>
                  Submit
                  {isLoading ? <CSpinner size="sm" className="ms-2" /> : null}
                </CButton>
              </div>
            </CForm>
          </CCardBody>
        </CCard>
      </div>
    </>
  )
}

export default DelAccountReq
