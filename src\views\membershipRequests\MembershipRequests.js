import React, { useState, useEffect } from "react";
import {
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CImage,
  CSpinner,
  CContainer,
} from "@coreui/react";
import {
  getDocs,
  collection,
  doc,
  deleteDoc,
  setDoc,
  getDoc,
} from "firebase/firestore";
import { db } from "src/firebase/firebase";
import Swal from "sweetalert2";
import moment from "moment";

const MembershipRequests = () => {
  const [requests, setRequests] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [modalData, setModalData] = useState({});
  const [selectedRequestId, setSelectedRequestId] = useState(null);

  const fetchRequests = async () => {
    setLoading(true);
    const requestsCollection = collection(db, "MembershipRequests");
    const requestsSnapshot = await getDocs(requestsCollection);
    const requestsList = await Promise.all(
      requestsSnapshot.docs.map(async (docs) => {
        const requestData = docs.data();
        const userDoc = await getDoc(doc(db, "Users", requestData.userId));
        const userData = userDoc.data();
        return {
          id: docs.id,
          ...requestData,
          profilePicture: userData?.profilePicture || "",
        };
      })
    );
    setRequests(requestsList);
    setLoading(false);
  };

  useEffect(() => {
    fetchRequests();
  }, []);

  const handleOpenDetailModal = (request) => {
    setModalData(request);
    setShowDetailModal(true);
  };

  const handleAcceptRequest = async (id) => {
    try {
      await setDoc(
        doc(db, "MembershipRequests", id),
        { status: "accepted" },
        { merge: true }
      );
      fetchRequests();
      Swal.fire("Accepted!", "Request has been accepted.", "success");
    } catch (error) {
      console.error("Error accepting request:", error);
      Swal.fire("Error", "There was an issue accepting the request.", "error");
    }
  };

  const handleDenyRequest = async (id) => {
    try {
      await setDoc(
        doc(db, "MembershipRequests", id),
        { status: "denied" },
        { merge: true }
      );
      fetchRequests();
      Swal.fire("Denied!", "Request has been denied.", "success");
    } catch (error) {
      console.error("Error denying request:", error);
      Swal.fire("Error", "There was an issue denying the request.", "error");
    }
  };

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    );
  }

  return (
    <CContainer>
      <h4>Membership Requests</h4>

      <CTable responsive>
        <CTableHead>
          <CTableRow>
            <CTableHeaderCell>#</CTableHeaderCell>
            <CTableHeaderCell>Profile Picture</CTableHeaderCell>
            <CTableHeaderCell>Name</CTableHeaderCell>
            <CTableHeaderCell>Email</CTableHeaderCell>
            <CTableHeaderCell>Membership</CTableHeaderCell>
            <CTableHeaderCell>Actions</CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {requests.map((request, index) => (
            <CTableRow key={index}>
              <CTableDataCell>{index + 1}</CTableDataCell>
              <CTableDataCell>
                <CImage
                  width={40}
                  height={40}
                  className="object-cover rounded"
                  src={request.profilePicture || ""}
                />
              </CTableDataCell>
              <CTableDataCell>{request.name}</CTableDataCell>
              <CTableDataCell>{request.email}</CTableDataCell>
              <CTableDataCell>{request.membership}</CTableDataCell>
              <CTableDataCell>
                <CButton
                  size="sm"
                  color="info text-white"
                  onClick={() => handleOpenDetailModal(request)}
                >
                  View
                </CButton>
                {request.status !== "accepted" && (
                  <CButton
                    size="sm"
                    color="success text-white ms-2"
                    onClick={() => handleAcceptRequest(request.id)}
                  >
                    Accept
                  </CButton>
                )}
                <CButton
                  size="sm"
                  color="danger text-white ms-2"
                  onClick={() => handleDenyRequest(request.id)}
                >
                  Deny
                </CButton>
              </CTableDataCell>
            </CTableRow>
          ))}
        </CTableBody>
      </CTable>

      {/* DETAILS MODAL */}
      <CModal
        visible={showDetailModal}
        onClose={() => setShowDetailModal(false)}
      >
        <CModalHeader>
          <CModalTitle>Request Details</CModalTitle>
        </CModalHeader>
        <CModalBody>
          <p>
            <strong>Profile Picture:</strong>
          </p>
          <CImage
            width={150}
            height={150}
            src={modalData.profilePicture}
            className="object-cover rounded mt-1 mb-4"
          />
          <p>
            <strong>Name:</strong> {modalData.name}
          </p>
          <p>
            <strong>Email:</strong> {modalData.email}
          </p>
          <p>
            <strong>Membership:</strong> {modalData.membership}
          </p>
          <p>
            <strong>Phone:</strong> {modalData.phone}
          </p>
          <p>
            <strong>Zip-code:</strong> {modalData.zipcode}
          </p>
          <p>
            <strong>City:</strong> {modalData.city}
          </p>
          <p>
            <strong>Country:</strong> {modalData.country}
          </p>
          <p>
            <strong>Card Name:</strong> {modalData.cardName}
          </p>
          <p>
            <strong>Card Number:</strong> {modalData.cardNumber}
          </p>
          <p>
            <strong>Expiry Date:</strong> {modalData.date}
          </p>
          <p>
            <strong>CVC:</strong> {modalData.cvc}
          </p>
          <p>
            <strong>Status:</strong> {modalData.status}
          </p>
        </CModalBody>
        <CModalFooter>
          <CButton
            color="secondary text-white ms-2"
            onClick={() => setShowDetailModal(false)}
          >
            Close
          </CButton>
          {modalData.status !== "accepted" && (
            <CButton
              color="success text-white ms-2"
              onClick={() => handleAcceptRequest(modalData.id)}
            >
              Accept
            </CButton>
          )}
          <CButton
            color="danger text-white ms-2"
            onClick={() => handleDenyRequest(modalData.id)}
          >
            Deny
          </CButton>
        </CModalFooter>
      </CModal>
    </CContainer>
  );
};

export default MembershipRequests;
