import React, { useState } from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CForm,
  CFormInput,
  CFormLabel,
  CFormTextarea,
  CRow,
  CSpinner,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilSend } from '@coreui/icons'
import { useForm } from 'react-hook-form'
import { joiResolver } from '@hookform/resolvers/joi'
import Joi from 'joi'
import { toast } from 'react-toastify'
import { addDoc, collection, getDocs } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'

const notificationSchema = Joi.object({
  title: Joi.string().required().min(3).messages({
    'string.base': 'Title is required.',
    'string.empty': 'Title is required.',
    'string.required': 'Title is required.',
    'string.min': 'Title must have 3 letters.',
  }),
  body: Joi.string().required().min(3).messages({
    'string.base': 'Body is required.',
    'string.empty': 'Body is required.',
    'string.required': 'Body is required.',
    'string.min': 'Body must have 3 letters.',
  }),
})

const SendNotifications = () => {
  const [isLoading, setLoading] = useState(false)
  const userId = localStorage.getItem('userId')

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: '',
      body: '',
    },
    mode: 'onBlur',
    resolver: joiResolver(notificationSchema),
  })

  // GET ALL USERS' TOKEN
  async function getAllTokens() {
    const arrOfTokens = []
    const usersDocs = await getDocs(collection(db, 'Users'))
    usersDocs.forEach((user) => {
      if (user?.data().fcmAuthToken) {
        arrOfTokens.push(user.data().fcmAuthToken)
      }
    })
    return arrOfTokens
  }

  // SEND NOTIFICATION
  async function sendNotifications(payload) {
    setLoading(true)
    try {
      const arrayOfTokens = await getAllTokens()
      const pl = {
        ...payload,
        userId,
        timestamp: new Date(),
        receiverToken: arrayOfTokens,
      }
      await addDoc(collection(db, 'Notifications'), pl)
      setLoading(false)
    } catch (error) {
      setLoading(false)
      toast.error('Something went wrong while sending notification.')
    }
  }
  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Send a notification to users</strong>
          </CCardHeader>
          <CCardBody>
            <CForm onSubmit={handleSubmit(sendNotifications)}>
              <CCol className="mb-2" md={3}>
                <CFormLabel htmlFor="title">Notification Title</CFormLabel>
                <CFormInput type="text" id="title" placeholder="Title" {...register('title')} />
              </CCol>
              {errors?.title ? <p className="text-danger mb-2">{errors?.title?.message}</p> : null}
              <CCol className="mb-2" md={6}>
                <CFormLabel htmlFor="body">Notification Body</CFormLabel>
                <CFormTextarea
                  id="body"
                  rows="3"
                  placeholder="Message"
                  {...register('body')}
                ></CFormTextarea>
              </CCol>
              {errors?.body ? <p className="text-danger mb-2">{errors?.body?.message}</p> : null}

              <CButton type="submit" color="primary" disabled={isLoading}>
                <CIcon icon={cilSend} className="me-2" />
                Send
                {isLoading ? <CSpinner className="ms-2" size="sm" /> : null}
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default SendNotifications
