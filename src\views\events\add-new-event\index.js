import React, { useState } from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CForm,
  CFormCheck,
  CFormInput,
  CFormLabel,
  CFormSelect,
  CRow,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPlus } from '@coreui/icons'
import 'react-datepicker/dist/react-datepicker.css'
import DatePicker from 'react-datepicker'
import RichTextEditor from 'react-rte'

const AddEvent = () => {
  // const [focused, setFocused] = useState()
  // const [startDate, setStartDate] = useState(new Date())
  // const [visible, setVisible] = useState(false)
  // const [taskDescription, setTaskDescription] = useState(
  //   RichTextEditor.createValueFromString('', 'markdown'),
  // )

  // const handleDateChange = (date) => {
  //   const d = new Date(date)
  //   setStartDate(d)
  // }

  // const onChangeDescription = (value) => {
  //   setTaskDescription(value)
  // }

  return (
    // <CRow>
    //   <CCol xs={12}>
    //     <CCard className="mb-4">
    //       <CCardHeader>
    //         <strong>Add Event</strong>
    //       </CCardHeader>
    //       <CCardBody>
    //         <CForm className="row g-3">
    //           <CCol md={3}>
    //             <CFormLabel htmlFor="inputZip">Winning Price</CFormLabel>
    //             <CFormInput id="inputZip" />
    //           </CCol>
    //           <CCol md={3}>
    //             <CFormLabel htmlFor="inputZip">Start Date</CFormLabel>
    //             <DatePicker
    //               className="form-control"
    //               selected={startDate}
    //               onChange={(date) => setStartDate(date)}
    //             />
    //           </CCol>
    //           <CCol md={3}>
    //             <CFormLabel htmlFor="inputZip">End Date</CFormLabel>
    //             <DatePicker
    //               className="form-control"
    //               selected={startDate}
    //               onChange={(date) => setStartDate(date)}
    //             />
    //           </CCol>
    //         </CForm>
    //         {/* <strong className="mt-4">Solo Tasks</strong> */}
    //         <div className="mt-5">
    //           <hr />
    //           <div className="d-flex align-items-center">
    //             <strong>Solo Tasks</strong>
    //             <div style={{ marginLeft: 16 }}>
    //               <CButton
    //                 onClick={() => setVisible(!visible)}
    //                 type="submit"
    //                 color="success"
    //                 variant="outline"
    //               >
    //                 Add Task
    //               </CButton>
    //             </div>
    //           </div>
    //           <hr />
    //         </div>
    //         <CTable striped>
    //           <CTableHead>
    //             <CTableRow>
    //               <CTableHeaderCell scope="col">#</CTableHeaderCell>
    //               <CTableHeaderCell scope="col">Task Name</CTableHeaderCell>
    //               <CTableHeaderCell scope="col">Description</CTableHeaderCell>
    //               <CTableHeaderCell scope="col">Points</CTableHeaderCell>
    //             </CTableRow>
    //           </CTableHead>
    //           <CTableBody>
    //             <CTableRow>
    //               <CTableHeaderCell scope="row">1</CTableHeaderCell>
    //               <CTableDataCell>Event 1</CTableDataCell>
    //               <CTableDataCell>$1200</CTableDataCell>
    //               <CTableDataCell>25-3-2023</CTableDataCell>
    //             </CTableRow>
    //             <CTableRow>
    //               <CTableHeaderCell scope="row">2</CTableHeaderCell>
    //               <CTableDataCell>Event 2</CTableDataCell>
    //               <CTableDataCell>$899</CTableDataCell>
    //               <CTableDataCell>27-3-2023</CTableDataCell>
    //             </CTableRow>
    //             <CTableRow>
    //               <CTableHeaderCell scope="row">3</CTableHeaderCell>
    //               <CTableDataCell>Event 3</CTableDataCell>
    //               <CTableDataCell>$1650</CTableDataCell>
    //               <CTableDataCell>02-4-2023</CTableDataCell>
    //             </CTableRow>
    //           </CTableBody>
    //         </CTable>

    //         <div className="mt-5">
    //           <hr />
    //           <div className="d-flex align-items-center">
    //             <strong>Group Tasks</strong>
    //             <div style={{ marginLeft: 16 }}>
    //               <CButton
    //                 onClick={() => setVisible(!visible)}
    //                 type="submit"
    //                 color="success"
    //                 variant="outline"
    //               >
    //                 Add Task
    //               </CButton>
    //             </div>
    //           </div>
    //           <hr />
    //         </div>
    //         <CTable striped>
    //           <CTableHead>
    //             <CTableRow>
    //               <CTableHeaderCell scope="col">#</CTableHeaderCell>
    //               <CTableHeaderCell scope="col">Task Name</CTableHeaderCell>
    //               <CTableHeaderCell scope="col">Description</CTableHeaderCell>
    //               <CTableHeaderCell scope="col">Points</CTableHeaderCell>
    //             </CTableRow>
    //           </CTableHead>
    //           <CTableBody>
    //             <CTableRow>
    //               <CTableHeaderCell scope="row">1</CTableHeaderCell>
    //               <CTableDataCell>Event 1</CTableDataCell>
    //               <CTableDataCell>$1200</CTableDataCell>
    //               <CTableDataCell>25-3-2023</CTableDataCell>
    //             </CTableRow>
    //             <CTableRow>
    //               <CTableHeaderCell scope="row">2</CTableHeaderCell>
    //               <CTableDataCell>Event 2</CTableDataCell>
    //               <CTableDataCell>$899</CTableDataCell>
    //               <CTableDataCell>27-3-2023</CTableDataCell>
    //             </CTableRow>
    //           </CTableBody>
    //         </CTable>

    //         <div className="mt-5">
    //           <hr />
    //           <div className="d-flex align-items-center">
    //             <strong>Duo Tasks</strong>
    //             <div style={{ marginLeft: 16 }}>
    //               <CButton
    //                 onClick={() => setVisible(!visible)}
    //                 type="submit"
    //                 color="success"
    //                 variant="outline"
    //               >
    //                 Add Task
    //               </CButton>
    //             </div>
    //           </div>
    //           <hr />
    //         </div>
    //         <CTable striped>
    //           <CTableHead>
    //             <CTableRow>
    //               <CTableHeaderCell scope="col">#</CTableHeaderCell>
    //               <CTableHeaderCell scope="col">Task Name</CTableHeaderCell>
    //               <CTableHeaderCell scope="col">Description</CTableHeaderCell>
    //               <CTableHeaderCell scope="col">Points</CTableHeaderCell>
    //             </CTableRow>
    //           </CTableHead>
    //           <CTableBody>
    //             <CTableRow>
    //               <CTableHeaderCell scope="row">1</CTableHeaderCell>
    //               <CTableDataCell>Event 1</CTableDataCell>
    //               <CTableDataCell>$1200</CTableDataCell>
    //               <CTableDataCell>25-3-2023</CTableDataCell>
    //             </CTableRow>
    //             <CTableRow>
    //               <CTableHeaderCell scope="row">2</CTableHeaderCell>
    //               <CTableDataCell>Event 2</CTableDataCell>
    //               <CTableDataCell>$899</CTableDataCell>
    //               <CTableDataCell>27-3-2023</CTableDataCell>
    //             </CTableRow>
    //           </CTableBody>
    //         </CTable>
    //       </CCardBody>
    //     </CCard>
    //   </CCol>

    //   <CModal visible={visible} onClose={() => setVisible(false)}>
    //     <CModalHeader onClose={() => setVisible(false)}>
    //       <CModalTitle>Add New Task</CModalTitle>
    //     </CModalHeader>
    //     <CModalBody>
    //       <CRow>
    //         <CCol md={6}>
    //           <CFormLabel htmlFor="inputZip">Task Name</CFormLabel>
    //           <CFormInput id="inputZip" />
    //         </CCol>
    //         <CCol md={6}>
    //           <CFormLabel htmlFor="inputZip">Task Points</CFormLabel>
    //           <CFormInput id="inputZip" />
    //         </CCol>
    //       </CRow>

    //       <CCol md={12} className="mt-4">
    //         <CFormLabel htmlFor="inputZip">Task Description</CFormLabel>
    //         <RichTextEditor value={taskDescription} onChange={onChangeDescription} />
    //       </CCol>
    //     </CModalBody>

    //     <CModalFooter>
    //       <CButton color="danger" onClick={() => setVisible(false)}>
    //         Close
    //       </CButton>
    //       <CButton color="success">Add Task</CButton>
    //     </CModalFooter>
    //   </CModal>
    // </CRow>
    null
  )
}

export default AddEvent
