import React from 'react'
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableCaption,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CContainer,
  CAvatar,
} from '@coreui/react'
import { DocsExample } from 'src/components'
import CIcon from '@coreui/icons-react'
import { cilPlus, cilCheckCircle, cilXCircle } from '@coreui/icons'

import avatar8 from './../../assets/images/avatars/2.jpg'

const subscriptions = [
  {
    invoice: 'EF4424-001',
    customerName: 'Faisal Ashraf',
    date: '15/3/23',
    dueDate: '05/5/23',
    amount: '$57',
    status: 'Paid',
    statusColor: 'success',
  },
  {
    invoice: 'EF4424-002',
    customerName: 'Ali Waqar',
    date: '11/3/23',
    dueDate: '26/4/23',
    amount: '$30',
    status: 'Due in 14 Days',
    statusColor: 'warning',
  },
  {
    invoice: 'EF4424-003',
    customerName: 'Abid Ali',
    date: '09/3/23',
    dueDate: '15/4/23',
    amount: '$20',
    status: 'Overdue by 1 day',
    statusColor: 'danger',
  },
  {
    invoice: 'EF4424-004',
    customerName: 'Zeeshan Virk',
    date: '03/3/23',
    dueDate: '13/3/23',
    amount: '$25',
    status: 'Paid',
    statusColor: 'success',
  },
]

const Subscriptions = () => {
  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex align-items-center justify-content-between">
              <strong>Subscriptions</strong>
            </div>
          </CCardHeader>
          <CCardBody>
            <CTable striped>
              <CTableHead>
                <CTableRow className="justify-content-center">
                  <CTableHeaderCell scope="col">Invoice Number</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Customer Name</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Date</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Due Date</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Amount</CTableHeaderCell>
                </CTableRow>
              </CTableHead>
              <CTableBody>
                {subscriptions.map((item, index) => (
                  <CTableRow key={index} align="middle" className="justify-content-center">
                    <CTableHeaderCell scope="row">{item.invoice}</CTableHeaderCell>
                    <CTableDataCell>{item.customerName}</CTableDataCell>
                    <CTableDataCell>
                      <CButton color={item.statusColor} shape="rounded-pill">
                        {item.status}
                      </CButton>
                    </CTableDataCell>
                    <CTableDataCell>{item.date}</CTableDataCell>
                    <CTableDataCell>{item.dueDate}</CTableDataCell>
                    <CTableDataCell>{item.amount}</CTableDataCell>
                  </CTableRow>
                ))}
              </CTableBody>
            </CTable>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Subscriptions
