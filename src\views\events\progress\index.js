import React from 'react'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableCaption,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CContainer,
  CNav,
  CNavItem,
  CNavLink,
  CAccordionBody,
  CAccordionHeader,
  CAccordionItem,
  CAccordion,
  CCardTitle,
  CAvatar,
} from '@coreui/react'
import { DocsExample } from 'src/components'
import CIcon from '@coreui/icons-react'
import { cilCheckCircle, cilPlus, cilXCircle } from '@coreui/icons'
import { Link } from 'react-router-dom'

import avatar8 from './../../../assets/images/avatars/2.jpg'

const users = [
  { name: '<PERSON>aisal Ashra<PERSON>', phone: '+123 44 78594' },
  { name: '<PERSON> Waq<PERSON>', phone: '+123 44 78594' },
  { name: '<PERSON>', phone: '+123 44 78594' },
  { name: '<PERSON><PERSON><PERSON>', phone: '+************' },
]

const Progress = () => {
  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex align-items-center justify-content-between">
              <strong>Event Progress</strong>
              {/* <Link to="/progress">
                <CButton type="submit" color="primary">
                  <CIcon icon={cilPlus} className="me-2" />
                  Add New Event
                </CButton>
              </Link> */}
            </div>
          </CCardHeader>

          <CCardBody>
            <div className="mb-2" />
            <CCardTitle style={{ color: '#1b9e3e', fontSize: 16 }}>Current Events</CCardTitle>
            <CAccordion activeItemKey={2}>
              <CAccordionItem itemKey={1}>
                <CAccordionHeader>Event Name 1</CAccordionHeader>
                <CAccordionBody>
                  You and your best friends, bridal party, or individuals who wanna team up!. Just
                  $5 for each individual that wants to sign up with you! Just change the quantity
                  amount to number of members and add their name in the notes section upon checking
                  out. Keep in mind some tasks will involve everyone in your team to be present, so
                  choose your team wisely!
                  <div className="mt-4" />
                  <CTable striped>
                    <CTableHead>
                      <CTableRow className="justify-content-center">
                        <CTableHeaderCell scope="col">Position</CTableHeaderCell>
                        <CTableHeaderCell scope="col">Avatar</CTableHeaderCell>
                        <CTableHeaderCell scope="col">Name</CTableHeaderCell>
                        <CTableHeaderCell scope="col">Phone</CTableHeaderCell>
                        <CTableHeaderCell scope="col">Progress</CTableHeaderCell>
                      </CTableRow>
                    </CTableHead>
                    <CTableBody>
                      {users.map((item, index) => (
                        <CTableRow key={index} align="middle" className="justify-content-center">
                          <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                          <CTableDataCell>
                            <CAvatar src={avatar8} size="md" />
                          </CTableDataCell>

                          <CTableDataCell>{item.name}</CTableDataCell>
                          <CTableDataCell>{item.phone}</CTableDataCell>
                          <CTableDataCell>
                            <Link to="/progress/user-progress">
                              <CButton color="info" variant="outline" shape="rounded-pill">
                                View Progress
                              </CButton>
                            </Link>
                          </CTableDataCell>
                        </CTableRow>
                      ))}
                    </CTableBody>
                  </CTable>
                </CAccordionBody>
              </CAccordionItem>
            </CAccordion>

            <div className="mb-4" />
            <CCardTitle style={{ color: '#80c6ff', fontSize: 16 }}>Past Events</CCardTitle>

            <CAccordion activeItemKey={2}>
              <CAccordionItem itemKey={1}>
                <CAccordionHeader>Event Name 1</CAccordionHeader>
                <CAccordionBody>
                  You and your best friends, bridal party, or individuals who wanna team up!. Just
                  $5 for each individual that wants to sign up with you! Just change the quantity
                  amount to number of members and add their name in the notes section upon checking
                  out. Keep in mind some tasks will involve everyone in your team to be present, so
                  choose your team wisely!
                  <div className="mt-4" />
                  <CTable striped>
                    <CTableHead>
                      <CTableRow className="justify-content-center">
                        <CTableHeaderCell scope="col">Position</CTableHeaderCell>
                        <CTableHeaderCell scope="col">Avatar</CTableHeaderCell>
                        <CTableHeaderCell scope="col">Name</CTableHeaderCell>
                        <CTableHeaderCell scope="col">Phone</CTableHeaderCell>
                        <CTableHeaderCell scope="col">Progress</CTableHeaderCell>
                      </CTableRow>
                    </CTableHead>
                    <CTableBody>
                      {users.map((item, index) => (
                        <CTableRow key={index} align="middle" className="justify-content-center">
                          <CTableHeaderCell scope="row">1</CTableHeaderCell>
                          <CTableDataCell>
                            <CAvatar src={avatar8} size="md" />
                          </CTableDataCell>

                          <CTableDataCell>{item.name}</CTableDataCell>
                          <CTableDataCell>{item.phone}</CTableDataCell>
                          <CTableDataCell>
                            <CButton color="info" variant="outline" shape="rounded-pill">
                              View Progress
                            </CButton>
                          </CTableDataCell>
                        </CTableRow>
                      ))}
                    </CTableBody>
                  </CTable>
                </CAccordionBody>
              </CAccordionItem>
            </CAccordion>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Progress
