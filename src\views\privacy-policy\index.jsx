import React, { useEffect, useState } from 'react'
import { collection, getDocs, query, where } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'

const PrivacyPolicy = () => {
  const [privacy, setPrivacy] = useState(null)

  // GET PRIVACY POLICY
  async function getPrivacyPolicyCotent() {
    const q = query(collection(db, 'AppData'), where('type', '==', 'policy'))
    await getDocs(q).then((response) => {
      setPrivacy({ id: response.docs[0].id, ...response.docs[0].data() })
    })
  }

  useEffect(() => {
    getPrivacyPolicyCotent()
  }, [])
  return (
    <>
      <div className="p-3" dangerouslySetInnerHTML={{ __html: privacy?.content }}></div>
    </>
  )
}

export default PrivacyPolicy
