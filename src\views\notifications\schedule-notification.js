import React, { useState } from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CForm,
  CFormInput,
  CFormLabel,
  CFormTextarea,
  CRow,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilSend } from '@coreui/icons'
import DatePicker from 'react-datepicker'

const ScheduleNotifications = () => {
  const [startDate, setStartDate] = useState(new Date())

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Send a notification to users</strong>
          </CCardHeader>
          <CCardBody>
            <CForm>
              <CCol className="mb-3" md={3}>
                <CFormLabel htmlFor="exampleFormControlInput1">Notification Title</CFormLabel>
                <CFormInput type="email" id="title" placeholder="title" />
              </CCol>
              <CCol className="mb-3" md={6}>
                <CFormLabel htmlFor="exampleFormControlTextarea1">Notification Message</CFormLabel>
                <CFormTextarea
                  id="exampleFormControlTextarea1"
                  rows="3"
                  placeholder="Message"
                ></CFormTextarea>
              </CCol>

              <CCol md={3} className="mb-3">
                <CFormLabel htmlFor="inputZip">Schedule Date-Time</CFormLabel>
                <DatePicker
                  className="form-control"
                  selected={startDate}
                  showTimeSelect
                  dateFormat="Pp"
                  onChange={(date) => setStartDate(date)}
                />
              </CCol>

              <CButton type="submit" color="primary">
                <CIcon icon={cilSend} className="me-2" />
                Send
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default ScheduleNotifications
