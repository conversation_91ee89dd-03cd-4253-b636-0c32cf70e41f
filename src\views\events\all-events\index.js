import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>ard<PERSON>ody,
  CCardHeader,
  <PERSON><PERSON>,
  CRow,
  CTable,
  CTableBody,
  CTableCaption,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CContainer,
} from '@coreui/react'
import { DocsExample } from 'src/components'
import CIcon from '@coreui/icons-react'
import { cilPlus } from '@coreui/icons'
import { Link } from 'react-router-dom'

const Events = () => {
  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex align-items-center justify-content-between">
              <strong>All Events</strong>
              <Link to="/events/add-new-event">
                <CButton type="submit" color="primary">
                  <CIcon icon={cilPlus} className="me-2" />
                  Add New Event
                </CButton>
              </Link>
            </div>
          </CCardHeader>
          <CCardBody>
            <CTable striped>
              <CTableHead>
                <CTableRow>
                  <CTableHeaderCell scope="col">#</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Name</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Winning Price</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Start Date</CTableHeaderCell>
                  <CTableHeaderCell scope="col">End Date</CTableHeaderCell>
                </CTableRow>
              </CTableHead>
              <CTableBody>
                <CTableRow>
                  <CTableHeaderCell scope="row">1</CTableHeaderCell>
                  <CTableDataCell>Event 1</CTableDataCell>
                  <CTableDataCell>$1200</CTableDataCell>
                  <CTableDataCell>25-3-2023</CTableDataCell>
                  <CTableDataCell>15-4-2023</CTableDataCell>
                </CTableRow>
                <CTableRow>
                  <CTableHeaderCell scope="row">2</CTableHeaderCell>
                  <CTableDataCell>Event 2</CTableDataCell>
                  <CTableDataCell>$899</CTableDataCell>
                  <CTableDataCell>27-3-2023</CTableDataCell>
                  <CTableDataCell>12-4-2023</CTableDataCell>
                </CTableRow>
                <CTableRow>
                  <CTableHeaderCell scope="row">3</CTableHeaderCell>
                  <CTableDataCell>Event 3</CTableDataCell>
                  <CTableDataCell>$1650</CTableDataCell>
                  <CTableDataCell>02-4-2023</CTableDataCell>
                  <CTableDataCell>20-5-2023</CTableDataCell>
                </CTableRow>
                <CTableRow>
                  <CTableHeaderCell scope="row">4</CTableHeaderCell>
                  {/* <CTableDataCell colSpan="2">Larry the Bird</CTableDataCell> */}
                  <CTableDataCell>Event 4</CTableDataCell>
                  <CTableDataCell>$2200</CTableDataCell>
                  <CTableDataCell>11-4-2023</CTableDataCell>
                  <CTableDataCell>16-4-2023</CTableDataCell>
                </CTableRow>
              </CTableBody>
            </CTable>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default Events
