/* eslint-disable react/prop-types */
import React from 'react'
import { CCard, CCardImage, CCardBody, CCardTitle, CButton } from '@coreui/react'

const PostCard = ({ post, viewDetails }) => {
  const imageStyles = { height: '10rem', objectFit: 'cover' }
  return (
    <>
      <CCard>
        <CCardImage orientation="top" src={post?.images[0]} style={imageStyles} />
        <CCardBody>
          <div className="d-flex justify-content-between">
            <CCardTitle className="text-truncate" title={post?.title}>
              {post?.title}
            </CCardTitle>
          </div>
          <p className="mb-1">Price: ${post?.price.toLocaleString()}</p>
          {/* <CCardText className="text-truncate mb-1">{category?.description}</CCardText> */}
          <CButton className="w-100" onClick={() => viewDetails(post)}>
            View Details
          </CButton>
        </CCardBody>
      </CCard>
    </>
  )
}

export default PostCard
