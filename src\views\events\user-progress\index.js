import React, { useState } from 'react'
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableCaption,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CContainer,
  CNav,
  CNavItem,
  CNavLink,
  CAccordionBody,
  CAccordionHeader,
  CAccordionItem,
  CAccordion,
  CCardTitle,
  CAvatar,
  CFormCheck,
  CCardText,
  CImage,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CFormLabel,
  CFormInput,
  CModalFooter,
  CProgress,
  CProgressBar,
} from '@coreui/react'
import { DocsExample } from 'src/components'
import CIcon from '@coreui/icons-react'
import { cilCheckCircle, cilPlus, cilStar, cilXCircle } from '@coreui/icons'
import { Link } from 'react-router-dom'

import avatar8 from './../../../assets/images/avatars/2.jpg'

const users = [
  { name: 'Task 1', isSubmitted: true, isApproved: true, points: 8 },
  { name: 'Task 2', isSubmitted: false, isApproved: false, points: 10 },
  { name: 'Task 3', isSubmitted: false, isApproved: false, points: 12 },
  { name: 'Task 4', isSubmitted: true, isApproved: false, points: 6 },
]

const UserProgress = () => {
  const [visible, setVisible] = useState(false)
  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex align-items-center justify-content-between">
              <strong>User Progress</strong>
              {/* <Link to="/progress">
                <CButton type="submit" color="primary">
                  <CIcon icon={cilPlus} className="me-2" />
                  Add New Event
                </CButton>
              </Link> */}
            </div>
          </CCardHeader>

          <div className="m-4">
            {[
              //   { color: 'primary', textColor: 'primary' },
              //   { color: 'secondary', textColor: 'secondary' },
              //   { color: 'success', textColor: 'success' },
              //   { color: 'danger', textColor: 'danger' },
              //   { color: 'warning', textColor: 'warning' },
              //   { color: 'info', textColor: 'info' },
              { color: 'dark' },
            ].map((item, index) => (
              <div key={index}>
                {/* <CImage rounded thumbnail src={avatar8} width={200} height={200} /> */}
                <CAvatar src={avatar8} size="xl" />

                <div className="mt-2">
                  <strong>Faisal Ashraf</strong>
                  <br />
                  <h7><EMAIL></h7>
                  <br />
                  <h7>+123 44 5555666</h7>
                </div>
              </div>
            ))}
          </div>

          <div style={{ marginLeft: 16, marginRight: 16 }}>
            <CProgress className="mb-3">
              <CProgressBar value={56}>56%</CProgressBar>
            </CProgress>
          </div>

          <CCardBody>
            <CTable striped>
              <CTableHead>
                <CTableRow className="justify-content-center">
                  <CTableHeaderCell scope="col">#</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Task Name</CTableHeaderCell>
                  <CTableHeaderCell scope="col">
                    <CTableHeaderCell scope="col">Points</CTableHeaderCell>
                  </CTableHeaderCell>
                  <CTableHeaderCell scope="col">Submitted</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Approved</CTableHeaderCell>
                  <CTableHeaderCell scope="col">View Submission</CTableHeaderCell>
                </CTableRow>
              </CTableHead>
              <CTableBody>
                {users.map((item, index) => (
                  <CTableRow key={index} align="middle" className="justify-content-center">
                    <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>

                    <CTableDataCell>{item.name}</CTableDataCell>
                    <CTableHeaderCell scope="col">
                      <CIcon icon={cilStar} className="me-2" style={{ color: 'orange' }} />

                      {item.points}
                    </CTableHeaderCell>

                    <CTableDataCell>
                      <CFormCheck id="flexCheckChecked" checked={item.isSubmitted} />
                    </CTableDataCell>

                    <CTableDataCell>
                      <CFormCheck id="flexCheckChecked" checked={item.isApproved} />
                    </CTableDataCell>
                    <CTableDataCell>
                      <CButton
                        color="info"
                        variant="outline"
                        shape="rounded-pill"
                        onClick={() => setVisible(true)}
                      >
                        Submission Details
                      </CButton>
                    </CTableDataCell>
                  </CTableRow>
                ))}
              </CTableBody>
            </CTable>
          </CCardBody>
        </CCard>
      </CCol>
      {/* <CModal
        className="show d-block"
        // backdrop={false}
        // keyboard={false}
        // portal={false}
        visible={visible}
      > */}
      <CModal visible={visible} onClose={() => setVisible(false)}>
        <CModalHeader>
          <CModalTitle>Task 1</CModalTitle>
        </CModalHeader>
        <CModalBody>
          Worem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit
          interdum, ac aliquet odio mattis. Class aptent taciti sociosqu ad litora torquent per
          conubia nostra, per inceptos himenaeos. Curabitur tempus urna at turpis condimentum
          lobortis. Ut commodo efficitur neque.
        </CModalBody>
        <CModalBody>
          <CTableHeaderCell scope="col">
            <h6 htmlFor="inputZip">Task Points</h6>
            <CIcon icon={cilStar} className="me-2" style={{ color: 'orange' }} />
            10
          </CTableHeaderCell>
        </CModalBody>
        <CModalBody>
          <h6 htmlFor="inputZip">Submitted Media</h6>
          <CImage rounded thumbnail src={avatar8} width={200} height={200} />
        </CModalBody>

        <CModalFooter>
          <CButton onClick={() => setVisible(false)} color="danger">
            Cancel
          </CButton>
          <CButton onClick={() => setVisible(false)} color="success">
            Approve Task
          </CButton>
        </CModalFooter>
      </CModal>
    </CRow>
  )
}

export default UserProgress
