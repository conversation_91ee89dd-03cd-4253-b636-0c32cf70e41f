/* eslint-disable react/prop-types */

import React, { useState, useEffect } from 'react'
import {
  CNav,
  CNavItem,
  CNavLink,
  CTabContent,
  CTabPane,
  CRow,
  CCol,
  CSpinner,
  CModal,
  CModalHeader,
  CModalBody,
  CImage,
  CButton,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilLocationPin } from '@coreui/icons'
import PostCard from 'src/components/PostCard'
import ImageSlider from 'src/components/ImageSlider'
import Status from 'src/components/Status'
import Tag from 'src/components/Tag'
import ActionButtons from 'src/components/ActionButtons'
import Person from '../../assets/images/avatars/person.png'
import { db } from 'src/firebase/firebase'
import {
  collection,
  getDocs,
  doc,
  updateDoc,
  getDoc,
  deleteDoc,
  limit,
  query,
  orderBy,
  writeBatch,
  serverTimestamp,
} from 'firebase/firestore'
import Swal from 'sweetalert2'
import { toast } from 'react-toastify'
import { deleteThisImage } from 'src/utils/FirebaseUtils'
import { FaPhoneAlt } from 'react-icons/fa'
import { MdLocationOn } from 'react-icons/md'
import logo from '../../assets/images/logo.png'

const Posts = () => {
  const [currentTab, setCurrentTab] = useState(1)
  const [posts, setPosts] = useState([])
  const [refresh, setRefresh] = useState(0)
  const [postDetails, setPostDetails] = useState(null)

  // MODAL HANDLERS - POST MODAL
  const [showPost, setShowPost] = useState(false)
  const handleShowPost = () => setShowPost(true)
  const handleClosePost = () => {
    setShowPost(false)
  }

  const [isLoading, setLoading] = useState(true)

  // GET ALL POSTS
  async function getAllPosts() {
    setLoading(true)
    let q = query(collection(db, 'Products'))
    // let q = query(collection(db, 'Products'), orderBy('createdAt', 'desc'))
    const postsDocs = await getDocs(q)
    const arrOfPromises = postsDocs.docs.map(async (post) => {
      const categoryId = post.data().categoryId
      const category = await getDoc(doc(db, 'Categories', categoryId))
      const ownerId = post.data().ownerId
      const owner = await getDoc(doc(db, 'Users', ownerId))
      return {
        id: post.id,
        ...post.data(),
        category: category.data(),
        owner: owner.data(),
      }
    })
    const postsArr = await Promise.all(arrOfPromises)

    const withCreatedAt = postsArr.filter((post) => post.createdAt !== undefined)
    const withoutCreatedAt = postsArr.filter((post) => post.createdAt === undefined)

    withCreatedAt.sort((a, b) => b.createdAt.toDate() - a.createdAt.toDate())

    const _sortedData = [...withCreatedAt, ...withoutCreatedAt]

    setPosts(_sortedData)
    setLoading(false)
  }
  // GET ALL POSTS
  useEffect(() => {
    getAllPosts()
    setCurrentTab(1)
  }, [refresh])

  // LOADING
  if (isLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )
  return (
    <>
      <h4>Posts</h4>
      <CNav variant="tabs" role="tablist" layout="fill">
        <CNavItem role="presentation">
          <CNavLink
            active={currentTab === 1}
            component="button"
            role="tab"
            aria-controls="pending-tab-pane"
            aria-selected={currentTab === 1}
            onClick={() => setCurrentTab(1)}
          >
            Pending
          </CNavLink>
        </CNavItem>
        <CNavItem role="presentation">
          <CNavLink
            active={currentTab === 2}
            component="button"
            role="tab"
            aria-controls="active-tab-pane"
            aria-selected={currentTab === 2}
            onClick={() => setCurrentTab(2)}
          >
            Approved
          </CNavLink>
        </CNavItem>
        <CNavItem role="presentation">
          <CNavLink
            active={currentTab === 3}
            component="button"
            role="tab"
            aria-controls="history-tab-pane"
            aria-selected={currentTab === 3}
            onClick={() => setCurrentTab(3)}
          >
            History
          </CNavLink>
        </CNavItem>
      </CNav>
      <CTabContent>
        {/* PENDING TAB CONTENT */}
        <CTabPane role="tabpanel" aria-labelledby="pending-tab-pane" visible={currentTab === 1}>
          <CRow>
            {posts.length
              ? posts
                  .filter((post) => post.status === 'pending')
                  .map((item, i) => {
                    // console.log('Rendering item>>', i, '>>', item.status)
                    return (
                      <CCol xl={3} lg={4} md={4} sm={6} key={i} className="mt-3 mb-2">
                        <PostCard
                          post={item}
                          viewDetails={(value) => {
                            setPostDetails(value)
                            handleShowPost()
                          }}
                        />
                      </CCol>
                    )
                  })
              : null}
          </CRow>
        </CTabPane>
        {/* ACTIVE TAB CONTENT */}
        <CTabPane role="tabpanel" aria-labelledby="active-tab-pane" visible={currentTab === 2}>
          <CRow>
            {posts
              ?.filter((post) => post.status === 'approved')
              .map((item, i) => {
                return (
                  <CCol xl={3} lg={4} md={4} sm={6} key={i} className="mt-3 mb-2">
                    <PostCard
                      post={item}
                      viewDetails={(value) => {
                        setPostDetails(value)
                        handleShowPost()
                      }}
                    />
                  </CCol>
                )
              })}
          </CRow>
        </CTabPane>
        {/* HISTORY TAB CONTENT */}
        <CTabPane role="tabpanel" aria-labelledby="history-tab-pane" visible={currentTab === 3}>
          <CRow>
            {posts
              ?.filter((post) => post.status === 'history')
              .map((item, i) => {
                return (
                  <CCol xl={3} lg={4} md={4} sm={6} key={i} className="mt-3 mb-2">
                    <PostCard
                      post={item}
                      viewDetails={(value) => {
                        setPostDetails(value)
                        handleShowPost()
                      }}
                    />
                  </CCol>
                )
              })}
          </CRow>
        </CTabPane>
      </CTabContent>

      {/* POST DETAILS */}
      <PostDetails
        post={postDetails}
        visible={showPost}
        closeModal={handleClosePost}
        setRefresh={setRefresh}
      />
    </>
  )
}

export default Posts

const PostDetails = ({ post, visible, closeModal, setRefresh }) => {
  const [isLoading, setLoading] = useState(false)
  // MARK AS ACTIVE
  const markAsApprovedThisPost = async () => {
    Swal.fire({
      title: 'Approve?',
      text: 'Are you sure to approve this post?',
      icon: 'question',
      confirmButtonColor: '#2eb85c',
      confirmButtonText: 'Confirm',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    })
      .then(async (result) => {
        if (result.isConfirmed) {
          setLoading(true)
          await updateDoc(doc(db, 'Products', post?.id), { status: 'approved' }).then(async () => {
            setRefresh((n) => n + 1)
            Swal.fire({
              title: 'Approved',
              text: 'Post approved successfully',
              icon: 'success',
            })
            closeModal()
          })
        }
      })
      .catch((error) => {
        toast.error('Something went wrong while aprroving the post.')
      })
  }

  //  DELET THIS POST
  async function deletThisPost() {
    Swal.fire({
      title: 'Delete?',
      text: 'Are you sure to delete this post?',
      icon: 'question',
      confirmButtonColor: '#e55353',
      confirmButtonText: 'Confirm',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    })
      .then(async (result) => {
        if (result.isConfirmed) {
          setLoading(true)
          await deleteDoc(doc(db, 'Products', post?.id)).then(() => {
            setRefresh((n) => n + 1)
            Swal.fire({
              title: 'Deleted',
              text: 'Post deleted successfully',
              icon: 'success',
            })
            closeModal()
          })
          post?.images?.forEach((imageURL) => {
            deleteThisImage(imageURL)
          })
        }
      })
      .catch((error) => {
        toast.error('Something went wrong while deleting the post.')
      })
  }

  return (
    <>
      <CModal visible={visible} onClose={closeModal} backdrop="static" size="lg">
        <CModalHeader>Post Details</CModalHeader>
        {!isLoading ? (
          <CModalBody>
            {/* IMAGES SLIDER */}
            <ImageSlider images={post?.images} />

            {/* ACTION BUTTONS */}
            <div className="d-flex align-items-center">
              <ActionButtons
                status={post?.status}
                // id={post?.id}
                approveThisPost={markAsApprovedThisPost}
                deleteThisPost={deletThisPost}
              />
              <h6 className="ms-auto mb-0">
                {post?.createdAt
                  ? new Date(post?.createdAt?.seconds * 1000).toLocaleString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: 'numeric',
                      second: 'numeric',
                    })
                  : null}
              </h6>
            </div>

            {/* PRICE AND STATUS */}
            <div className="d-flex justify-content-between align-items-center mt-1">
              <p className="m-0">Price: ${post?.price.toLocaleString()}</p>
              <Status status={post?.status} approveThisPost={markAsApprovedThisPost} />
            </div>

            {/* POST INFO */}
            <h5 className="mt-2">{post?.title}</h5>
            <p className="mt-2">{post?.description}</p>

            <p className="mt-2 d-flex align-items-center">
              <CImage
                src={post?.category?.image}
                style={{ height: 36, width: 36 }}
                className="rounded-circle"
              />
              <span className="ms-2">{post?.category?.name}</span>
            </p>
            <p className="mt-2 d-flex align-items-center">
              <MdLocationOn className="me-1 h2 mb-0" />
              <span className="ms-2">{post?.location}</span>
            </p>
            <p className="mt-2 d-flex align-items-center">
              <CImage
                src={post?.owner?.profile || logo}
                width={36}
                height={36}
                className="me-1 object-cover border rounded-circle"
              />
              <span className="ms-2">{post?.owner?.name}</span>
            </p>
            <p className="mt-2 d-flex align-items-center">
              <FaPhoneAlt className="me-1 h2 mb-0" />
              <span className="ms-2">{post?.owner?.phoneNumber}</span>
            </p>

            {/* TAGS */}
            <h6>Tags</h6>
            {post?.tags?.map((item, i) => {
              return <Tag str={item} key={i} />
            })}
          </CModalBody>
        ) : (
          <div className="d-flex justify-content-center align-items-center my-4">
            <CSpinner />
          </div>
        )}
      </CModal>
    </>
  )
}
