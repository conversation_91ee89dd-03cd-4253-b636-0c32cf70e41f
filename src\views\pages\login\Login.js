import React, { useState } from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardGroup,
  CCol,
  CContainer,
  CForm,
  CFormInput,
  CImage,
  CInputGroup,
  CInputGroupText,
  CRow,
  CSpinner,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilLockLocked, cilUser } from '@coreui/icons'
import { useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { joiResolver } from '@hookform/resolvers/joi'
import Joi from 'joi'
import { signInWithEmailAndPassword } from 'firebase/auth'
import { doc, getDoc } from 'firebase/firestore'
import { auth, db } from '../../../firebase/firebase'
import { toast } from 'react-toastify'
import logo from '../../../assets/images/logo.png'

const Login = () => {
  const [isLoading, setLoading] = useState(false)
  const navigate = useNavigate()

  const loginSchema = Joi.object({
    email: Joi.string()
      .email({ tlds: { allow: false } })
      .required()
      .messages({
        'string.empty': 'Email is required.',
        'string.required': 'Email is required.',
        'string.email': 'Email must be valid.',
      }),
    password: Joi.string().min(6).required().messages({
      'string.empty': 'Password is required.',
      'string.required': 'Password is required.',
      'string.min': 'Password must have 6 letters.',
    }),
  })
  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    mode: 'onBlur',
    resolver: joiResolver(loginSchema),
  })

  // LOGIN WITH CREDENTIALS
  async function loginWithCredentials({ email, password }) {
    setLoading(true)
    try {
      const response = await signInWithEmailAndPassword(auth, email, password)

      localStorage.setItem('userId', response?.user?.uid)
      const userData = await fetchUser(response?.user?.uid)

      // USER IS ADMIN
      if (userData && userData.isAdmin) {
        localStorage.setItem('userData', JSON.stringify(userData))
        localStorage.setItem('accessToken', response?.user?.accessToken)
        toast.success('Login successful!')
        navigate('/users')
      } else if (userData && !userData.isAdmin) {
        toast.error('Access denied. Admin privileges required.')
      } else {
        toast.error('User profile not found. Please contact administrator.')
      }

      setLoading(false)
    } catch (error) {
      console.error('Login error:', error)
      setLoading(false)

      // Handle specific Firebase auth errors
      switch (error.code) {
        case 'auth/user-not-found':
          toast.error('This email does not exist.')
          break
        case 'auth/wrong-password':
          toast.error('Invalid email or password')
          break
        case 'auth/invalid-email':
          toast.error('Invalid email format')
          break
        case 'auth/user-disabled':
          toast.error('This account has been disabled')
          break
        case 'auth/too-many-requests':
          toast.error('Too many failed attempts. Please try again later.')
          break
        default:
          toast.error('Login failed. Please try again.')
      }
    }
  }


  // GET USER THROUGHT UUID
  const fetchUser = async (id) => {
    const userRef = doc(db, 'Users', id)
    const userSnap = await getDoc(userRef)
    if (userSnap.exists()) {
      return userSnap.data()
    } else return null
  }

  return (
    <>
      <div className="bg-light min-vh-100 d-flex flex-row align-items-center">
        <CContainer>
          <CRow className="justify-content-center">
            <CCol md={6}>
              <CCardGroup>
                <CCard className="p-4">
                  <CCardBody>
                    <div className="text-center">
                      <CImage src={logo} height={100} />
                    </div>
                    <CForm onSubmit={handleSubmit(loginWithCredentials)}>
                      <h1>Login</h1>
                      <p className="text-medium-emphasis">Login In to your account</p>
                      <CInputGroup className="mb-2">
                        <CInputGroupText>
                          <CIcon icon={cilUser} />
                        </CInputGroupText>
                        <CFormInput
                          placeholder="Username"
                          autoComplete="username"
                          {...register('email')}
                        />
                      </CInputGroup>
                      {errors?.email ? (
                        <p className="text-danger">{errors?.email.message}</p>
                      ) : null}
                      <CInputGroup className="mb-2">
                        <CInputGroupText>
                          <CIcon icon={cilLockLocked} />
                        </CInputGroupText>
                        <CFormInput
                          type="password"
                          placeholder="Password"
                          autoComplete="current-password"
                          {...register('password')}
                        />
                      </CInputGroup>
                      {errors?.password ? (
                        <p className="text-danger">{errors?.password?.message}</p>
                      ) : null}
                      <CRow>
                        <CCol xs={6} className="text-center">
                          <CButton
                            className="px-4 d-flex justify-content-center align-items-center"
                            type="submit"
                            // style={{
                            //   backgroundColor: '#FF8C0E',
                            //   borderColor: '#FF8C0E',
                            // }}
                            disabled={isLoading}
                          >
                            Login
                            {isLoading ? <CSpinner className="ms-2" size="sm" /> : null}
                          </CButton>
                        </CCol>
                      </CRow>
                    </CForm>
                  </CCardBody>
                </CCard>
              </CCardGroup>
            </CCol>
          </CRow>
        </CContainer>
      </div>
    </>
  )
}

export default Login
