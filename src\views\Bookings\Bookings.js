/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react'
import {
  <PERSON>utton,
  CSpinner,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CFormSelect,
} from '@coreui/react'
import Swal from 'sweetalert2'
import { toast } from 'react-toastify'
import { collection, deleteDoc, doc, getDocs, getDoc, where, query } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'
import { cilTrash } from '@coreui/icons'
import CIcon from '@coreui/icons-react'
import EmptyBox from 'src/components/EmptyBox'
import moment from 'moment'

const Bookings = () => {
  const [bookings, setBookings] = useState([])
  const [isLoading, setLoading] = useState(true)
  const [modalVisible, setModalVisible] = useState(false)
  const [selectedBooking, setSelectedBooking] = useState(null)
  const [currentPage, setCurrentPage] = useState(0)
  const [pageSize] = useState(10)
  const [totalBookings, setTotalBookings] = useState(0)
  const [statusFilter, setStatusFilter] = useState('All')

  // GET ALL BOOKINGS
  const getAllBookings = async (page = 0) => {
    try {
      setLoading(true)
      let bookingsQuery = collection(db, 'Bookings')

      if (statusFilter !== 'All') {
        bookingsQuery = query(bookingsQuery, where('status', '==', statusFilter))
      }

      const bookingsSnapshot = await getDocs(bookingsQuery)
      const arrOfBookings = []

      setTotalBookings(bookingsSnapshot.size)
      const fetchDetailsPromises = bookingsSnapshot.docs.map(async (bookingDoc) => {
        const bookingData = bookingDoc.data()
        const userPromise = getDoc(doc(db, 'Users', bookingData.userId))
        const petPromise = getDoc(doc(db, 'Pets', bookingData.petId))

        const [userDoc, petDoc] = await Promise.all([userPromise, petPromise])
        const bookingObject = {
          id: bookingDoc.id,
          ...bookingData,
          user: userDoc.exists() ? userDoc.data() : null,
          pet: petDoc.exists() ? petDoc.data() : null,
        }
        arrOfBookings.push(bookingObject)
      })

      await Promise.all(fetchDetailsPromises)

      // Paginate bookings
      const startIndex = page * pageSize
      const paginatedBookings = arrOfBookings.slice(startIndex, startIndex + pageSize)

      setBookings(paginatedBookings)
    } catch (error) {
      toast.error('Error fetching bookings.')
    } finally {
      setLoading(false)
    }
  }

  // DELETE THIS BOOKING
  const deleteThisBooking = async (booking) => {
    const result = await Swal.fire({
      title: 'Delete?',
      text: 'Are you sure to delete this booking?',
      icon: 'question',
      confirmButtonColor: '#e55353',
      confirmButtonText: 'Confirm',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    })

    if (result.isConfirmed) {
      setLoading(true)
      try {
        await deleteDoc(doc(db, 'Bookings', booking?.id))
        toast.success('Booking deleted successfully.')
        getAllBookings(currentPage)
      } catch (error) {
        toast.error('Something went wrong while deleting the booking.')
      } finally {
        setLoading(false)
      }
    }
  }

  useEffect(() => {
    getAllBookings(currentPage)
  }, [currentPage, statusFilter])

  const openModal = (booking) => {
    setSelectedBooking(booking)
    setModalVisible(true)
  }

  const closeModal = () => {
    setSelectedBooking(null)
    setModalVisible(false)
  }

  // Handle filter change
  const handleFilterChange = (e) => {
    setStatusFilter(e.target.value)
    setCurrentPage(0)
  }

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )
  }

  return (
    <>
      <h4>Bookings</h4>

      {/* FILTER BUTTON AND DROPDOWN */}
      <div className="d-flex mb-3">
        <CFormSelect
          aria-label="Filter by status"
          value={statusFilter}
          onChange={handleFilterChange}
          className="w-25"
        >
          <option value="All">All Bookings</option>
          <option value="pending">Pending</option>
          <option value="accepted">Accepted</option>
          <option value="cancelled">Cancelled</option>
        </CFormSelect>
      </div>

      {/* TABLE */}
      {bookings.length ? (
        <>
          <CTable style={{ verticalAlign: 'middle' }}>
            <CTableHead>
              <CTableRow>
                <CTableHeaderCell scope="col">#</CTableHeaderCell>
                <CTableHeaderCell scope="col">Picture</CTableHeaderCell>

                <CTableHeaderCell scope="col">Author</CTableHeaderCell>
                <CTableHeaderCell scope="col">Start Date</CTableHeaderCell>
                <CTableHeaderCell scope="col">Status</CTableHeaderCell>
                <CTableHeaderCell scope="col">Price</CTableHeaderCell>
                <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              {bookings.map((item, i) => (
                <CTableRow key={i}>
                  <CTableHeaderCell scope="row">{currentPage * pageSize + i + 1}</CTableHeaderCell>
                  <CTableDataCell>
                    <img
                      src={item?.pet.picture} // Display pet picture
                      alt="Pet"
                      style={{ width: 50, height: 50, objectFit: 'cover', borderRadius: 5 }}
                    />
                  </CTableDataCell>
                  <CTableDataCell>
                    {item?.user.firstName} {item?.user.surName}
                  </CTableDataCell>
                  <CTableDataCell>{item?.bookingDate.startDate}</CTableDataCell>
                  <CTableDataCell>{item?.status}</CTableDataCell>
                  <CTableDataCell>{item?.price}</CTableDataCell>

                  <CTableDataCell className="action-buttons">
                    <CButton
                      color="primary"
                      size="sm"
                      className="ms-1"
                      onClick={() => openModal(item)}
                    >
                      View Details
                    </CButton>
                    <CButton
                      color="danger"
                      size="sm"
                      className="ms-1"
                      onClick={() => deleteThisBooking(item)}
                    >
                      <CIcon icon={cilTrash} className="text-light" />
                    </CButton>
                  </CTableDataCell>
                </CTableRow>
              ))}
            </CTableBody>
          </CTable>
          {/* PAGINATION */}
          <div className="d-flex justify-content-center gap-4">
            <button
              onClick={() => setCurrentPage(0)}
              disabled={currentPage === 0}
              className="btn btn-secondary"
            >
              {'<<'}
            </button>
            <button
              onClick={() => setCurrentPage((prev) => prev - 1)}
              disabled={currentPage === 0}
              className="btn btn-secondary"
            >
              {'<'}
            </button>
            <button
              onClick={() => setCurrentPage((prev) => prev + 1)}
              disabled={bookings.length < pageSize}
              className="btn btn-secondary"
            >
              {'>'}
            </button>
            <button
              onClick={() => setCurrentPage(Math.ceil(totalBookings / pageSize) - 1)}
              disabled={bookings.length < pageSize}
              className="btn btn-secondary"
            >
              {'>>'}
            </button>
          </div>
        </>
      ) : (
        <EmptyBox label="No bookings yet.." />
      )}

      {/* MODAL */}
      {selectedBooking && (
        <CModal visible={modalVisible} onClose={closeModal}>
          <CModalHeader onClose={closeModal}>
            <CModalTitle>Booking Details</CModalTitle>
          </CModalHeader>
          <CModalBody>
            <p>
              <strong>Owner name:</strong> {selectedBooking.user.firstName}{' '}
              {selectedBooking.user.surName}
            </p>
            <p>
              <strong>Pet name:</strong> {selectedBooking.pet.name}
            </p>
            <p>
              <strong>Pet age:</strong> {selectedBooking.pet.age} {selectedBooking.pet.ageUnit}
            </p>
            <p>
              <strong>Email:</strong> {selectedBooking.user.email}
            </p>
            <p>
              <strong>Booking start time:</strong> {selectedBooking.bookingDate.startDate}
            </p>
            <p>
              <strong>Booking end time:</strong> {selectedBooking.bookingDate.endDate}
            </p>
            <p>
              <strong>Booking created:</strong>{' '}
              {moment(selectedBooking.createdAt.seconds * 1000).format('DD MMM YYYY, hh:mm a')}
            </p>
            <p>
              <strong>Price:</strong> {selectedBooking.price}
            </p>
            <p>
              <strong>Status:</strong> {selectedBooking.status}
            </p>
            <p>
              <strong>Services:</strong>
            </p>
            <ol>
              {selectedBooking?.services?.map((service, index) => (
                <li key={index}>
                  {service}
                  <br />
                </li>
              ))}
            </ol>
          </CModalBody>
          <CModalFooter>
            <CButton
              color="danger"
              onClick={() => deleteThisBooking(selectedBooking)}
              className="text-light"
            >
              Delete <CIcon icon={cilTrash} className="text-light" />
            </CButton>
            <CButton color="secondary" onClick={closeModal} className="text-light">
              Close
            </CButton>
          </CModalFooter>
        </CModal>
      )}
    </>
  )
}

export default Bookings
