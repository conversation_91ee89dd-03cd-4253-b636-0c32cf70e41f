/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react'
import {
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CButton,
  CImage,
  CModal,
  CModalHeader,
  CModalBody,
  CForm,
  CFormLabel,
  CFormInput,
  CSpinner,
} from '@coreui/react'
import CIcon from '@coreui/icons-react'
import { cilPencil, cilTrash } from '@coreui/icons'
import { useForm } from 'react-hook-form'
import { joiResolver } from '@hookform/resolvers/joi'
import Joi from 'joi'
import { toast } from 'react-toastify'
import { addDoc, collection, getDocs, updateDoc, doc, deleteDoc } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'
import { deleteThisImage, uploadThisImage } from 'src/utils/FirebaseUtils'
import Swal from 'sweetalert2'

const Dashboard = () => {
  const [categories, setCategories] = useState(null)
  const [isLoading, setLoading] = useState(true)
  const [refresh, setRefresh] = useState(0)
  const [editCategory, setEditCategory] = useState(null)
  // MODAL HANDLERS - ADD CATEGORY MODAL
  const [showCategory, setShowCategory] = useState(false)
  const handleShowCategory = () => setShowCategory(true)
  const handleCloseCategory = () => {
    setShowCategory(false)
    setEditCategory(null)
  }

  const imageStyle = { width: 34, height: 34, aspectRatio: '1/1' }
  // GET ALL CATEGORIES
  async function getAllCategories() {
    setLoading(true)
    const arrOfCategories = []
    const categoriesDocs = await getDocs(collection(db, 'Categories'))
    categoriesDocs.forEach((category) => {
      arrOfCategories.push({ id: category.id, ...category.data() })
    })

    setCategories(arrOfCategories)
    setLoading(false)
  }
  // HANDLE EDIT
  const handleEdit = (category) => {
    setEditCategory(category)
    handleShowCategory()
  }

  // DELETE CATEGORY AND IMAGE
  const deleteThisCategory = async (category) => {
    Swal.fire({
      title: 'Delete?',
      text: 'Are you sure to delete this category?',
      icon: 'question',
      confirmButtonColor: '#e55353',
      confirmButtonText: 'Confirm',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        setLoading(true)
        await deleteDoc(doc(db, 'Categories', category?.id))
          .then(async () => {
            await deleteThisImage(category?.image)
            Swal.fire({
              title: 'Deleted',
              text: 'Category deleted successfully',
              icon: 'success',
            })
            setRefresh((n) => n + 1)
          })
          .catch((error) => {
            setLoading(false)
            toast.error('Something went wrong while deleting the category.')
          })
      }
    })
  }

  useEffect(() => {
    getAllCategories()
  }, [refresh])

  if (isLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )
  return (
    <>
      <div className="d-flex justify-content-between mb-2">
        <h4>Categories</h4>
        <CButton color="primary" className="text-light" size="sm" onClick={handleShowCategory}>
          Add category
        </CButton>
      </div>
      <CTable style={{ verticalAlign: 'middle' }}>
        <CTableHead>
          <CTableRow>
            <CTableHeaderCell scope="col">#</CTableHeaderCell>
            <CTableHeaderCell scope="col">Image</CTableHeaderCell>
            <CTableHeaderCell scope="col" className="w-50">
              Title
            </CTableHeaderCell>
            <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
          </CTableRow>
        </CTableHead>
        <CTableBody>
          {categories?.map((item, i) => {
            return (
              <CTableRow key={i}>
                <CTableHeaderCell scope="row">{i + 1}</CTableHeaderCell>
                <CTableDataCell>
                  <CImage src={item.image} style={imageStyle} />
                </CTableDataCell>
                <CTableDataCell>{item.name}</CTableDataCell>
                <CTableDataCell className="action-buttons">
                  <CButton color="secondary" size="sm" onClick={() => handleEdit(item)}>
                    <CIcon icon={cilPencil} className="text-light" />
                  </CButton>
                  <CButton
                    color="danger"
                    size="sm"
                    className="ms-1"
                    onClick={() => deleteThisCategory(item)}
                  >
                    <CIcon icon={cilTrash} className="text-light" />
                  </CButton>
                </CTableDataCell>
              </CTableRow>
            )
          })}
        </CTableBody>
      </CTable>

      {/* ADD CATEGORY MODAL */}
      <AddCategoryModal
        visible={showCategory}
        closeModal={handleCloseCategory}
        category={editCategory}
        setRefresh={setRefresh}
      />
    </>
  )
}

export default Dashboard

const AddCategoryModal = ({ visible, closeModal, category, setRefresh }) => {
  const [isLoading, setLoading] = useState(false)
  const categorySchema = Joi.object({
    name: Joi.string().required().min(3).messages({
      'string.base': 'Title is required.',
      'string.empty': 'Title is required.',
      'string.required': 'Title is required.',
      'string.min': 'Title must have 3 letters.',
    }),
    image: Joi.string().empty().required().messages({
      'string.base': 'Image is required.',
      'string.empty': 'Image is required.',
      'string.required': 'Image is required.',
    }),
  })

  const {
    handleSubmit,
    register,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm({
    defaultValues: {
      name: '',
      image: '',
    },
    mode: 'onBlur',
    resolver: joiResolver(categorySchema),
  })
  const formData = watch()

  // SUBMIT FORM
  async function submitForm({ name, image }) {
    setLoading(true)
    try {
      // ADD NEW DOC
      if (!category) {
        const url = await uploadThisImage(image, 'Categories')
        await addDoc(collection(db, 'Categories'), {
          name,
          image: url,
          productCount: 0,
        })
      }
      // EDIT EXSITING DOC
      if (category) {
        let payload = { name }
        // UPDATE IMAGE
        if (category?.image !== image) {
          const newURL = await uploadThisImage(image)
          payload.image = newURL
          await deleteThisImage(category?.image)
        }
        // ONLY CATEGORY
        await updateDoc(doc(db, 'Categories', category?.id), payload)
      }
      closeModal()
      setRefresh((n) => n + 1)
      setLoading(false)
    } catch (error) {
      setLoading(false)
      toast.error('Something went wrong.')
      // console.error('Error uploading image to Firebase:', error)
    }
  }

  // HANDLE CHANGE IMAGE OF FORM
  const handleChangeImage = async (event) => {
    const file = event?.target?.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const dataUrl = e.target.result
        setValue('image', dataUrl)
      }
      reader.readAsDataURL(file)
    }
  }

  // ON MOUNT
  useEffect(() => {
    if (category) {
      setValue('name', category?.name)
      setValue('image', category?.image)
    }
  }, [category])
  return (
    <>
      <CModal
        visible={visible}
        onClose={() => {
          closeModal()
          reset()
        }}
        size="lg"
      >
        <CModalHeader closeButton>Add New Category</CModalHeader>
        <CModalBody>
          {/* FORM */}
          <CForm onSubmit={handleSubmit(submitForm)}>
            {/* TITLE */}
            <CFormLabel htmlFor="title">Title</CFormLabel>
            <CFormInput type="text" {...register('name')} id="name" />
            {errors?.name ? <p className="m-0 text-danger">{errors?.name?.message}</p> : null}

            {/* IMAGE */}
            <CFormLabel htmlFor="image" className="mt-2">
              Image
            </CFormLabel>
            <CFormInput
              type="file"
              // {...register('image')}
              id="image"
              accept="image/*"
              onChange={(event) => handleChangeImage(event)}
            />
            {errors?.image ? <p className="m-0 text-danger">{errors?.image?.message}</p> : null}

            {formData?.image ? (
              <div className="d-flex">
                <img
                  src={formData?.image}
                  className="rounded border mt-2 mx-auto"
                  style={{ width: '8rem', height: '8rem', aspectRatio: '1/1' }}
                />
              </div>
            ) : null}

            {/* SUBMIT BUTTON */}
            <div className="mt-3 text-end">
              <CButton type="submit" disabled={isLoading}>
                Submit
                {isLoading ? <CSpinner size="sm" className="ms-2" /> : null}
              </CButton>
            </div>
          </CForm>
        </CModalBody>
      </CModal>
    </>
  )
}
