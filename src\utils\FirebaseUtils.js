import { uploadString, ref, getDownloadURL, deleteObject } from 'firebase/storage'
import { storage } from 'src/firebase/firebase'

// UPLOAD IMAGE
const uploadThisImage = async (image, folder) => {
  let str = (Math.random() + 1).toString(36).substring(7)
  const uploadRef = ref(storage, `${folder}/${str}`)
  const uploadedImageRef = await uploadString(uploadRef, image, 'data_url')
  const url = await getDownloadURL(uploadedImageRef.ref)
  return url
}
// DELETE IMAGE
const deleteThisImage = async (url) => {
  const oldImageRef = ref(storage, url)
  await deleteObject(oldImageRef)
}

export { uploadThisImage, deleteThisImage }
