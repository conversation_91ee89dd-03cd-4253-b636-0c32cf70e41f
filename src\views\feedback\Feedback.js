/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react'
import {
  CSpinner,
  CImage,
  CCard,
  CCardBody,
  CCardTitle,
  CCardSubtitle,
  CCardText,
  CButton,
} from '@coreui/react'
import { collection, deleteDoc, doc, getDocs } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'
import EmptyBox from 'src/components/EmptyBox'
import Placeholder from '../../assets/images/avatars/avatar_placeholder.png'
import { cilTrash } from '@coreui/icons'
import CIcon from '@coreui/icons-react'
import Swal from 'sweetalert2'

const Feedback = () => {
  const [feedback, setFeedback] = useState([])
  const [isLoading, setLoading] = useState(true)
  const [refresh, setRefresh] = useState(0)

  // GET ALL CATEGORIES
  async function getAllUsers() {
    setLoading(true)
    const arrOfFeedback = []
    const feedbackDocs = await getDocs(collection(db, 'Feedback'))
    feedbackDocs.forEach((feedback) => {
      arrOfFeedback.push({ id: feedback.id, ...feedback.data() })
    })

    setFeedback(arrOfFeedback)
    setLoading(false)
  }

  // DELETE FEEDBACK
  async function deleteFeedback(thisFeedback) {
    Swal.fire({
      title: 'Delete?',
      text: 'Are you sure to delete this feedback?',
      icon: 'question',
      confirmButtonColor: '#e55353',
      confirmButtonText: 'Confirm',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        await deleteDoc(doc(db, 'Feedback', thisFeedback?.id))
        setRefresh((n) => n + 1)
      }
    })
  }

  useEffect(() => {
    getAllUsers()
  }, [refresh])

  // LOADING
  if (isLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )

  return (
    <>
      {feedback.length ? (
        <>
          <h4>Feedback</h4>
          {feedback.map((item, i) => {
            return (
              <CCard key={i} className="mb-2">
                <CCardBody className="position-relative">
                  <CButton
                    size="sm"
                    color="danger text-white ms-2 position-absolute top-0 end-0"
                    onClick={() => deleteFeedback(item)}
                  >
                    <CIcon icon={cilTrash} />
                  </CButton>
                  <div className="d-flex align-items-center">
                    <CImage
                      src={item?.userProfile ? item?.userProfile : Placeholder}
                      style={{ height: 56, width: 56, aspectRatio: '1/1' }}
                      className="rounded-circle"
                    />
                    <div className="ms-2 d-flex flex-column justify-content-center">
                      <CCardTitle className="h5 mb-1">{item?.name}</CCardTitle>
                      <CCardSubtitle className="m-0 p text-medium-emphasis">
                        {item?.email}
                      </CCardSubtitle>
                    </div>
                  </div>
                  <CCardText style={{ marginLeft: 65, marginTop: 5 }}>{item?.feedback}</CCardText>
                </CCardBody>
              </CCard>
            )
          })}
        </>
      ) : (
        <EmptyBox label={'No Feedback available..'} />
      )}
    </>
  )
}

export default Feedback
