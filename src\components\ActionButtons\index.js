/* eslint-disable react/prop-types */
import React from 'react'
import { CButton } from '@coreui/react'
import { cilPencil, cilTrash } from '@coreui/icons'
import CIcon from '@coreui/icons-react'

const ActionButtons = ({ status, approveThisPost, deleteThisPost }) => {
  return (
    <>
      <div className="mt-3">
        {status === 'pending' ? (
          <CButton className="btn-sm me-2" onClick={() => approveThisPost()}>
            Approve Post
            <CIcon icon={cilPencil} height={16} width={16} className="ms-2 white-icon" />
          </CButton>
        ) : null}
        <CButton className="btn-sm btn-danger text-light" onClick={() => deleteThisPost()}>
          Delete Post
          <CIcon icon={cilTrash} height={16} width={16} className="ms-2 white-icon" />
        </CButton>
      </div>
    </>
  )
}

export default ActionButtons
