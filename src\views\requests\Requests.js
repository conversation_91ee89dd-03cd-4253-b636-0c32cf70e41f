/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>pinner,
  CTable,
  CTableHead,
  CTableRow,
  CTableHeaderCell,
  CTableBody,
  CTableDataCell,
  CBadge,
} from '@coreui/react'
import Swal from 'sweetalert2'
import { toast } from 'react-toastify'
import { collection, deleteDoc, doc, getDoc, getDocs, updateDoc } from 'firebase/firestore'
import { auth, db } from 'src/firebase/firebase'
import { cilTrash, cilCheckCircle, cilXCircle } from '@coreui/icons'
import CIcon from '@coreui/icons-react'
import EmptyBox from 'src/components/EmptyBox'
import moment from 'moment'

const Requests = () => {
  const [requests, setRequests] = useState([])
  const [isLoading, setLoading] = useState(true)

  // GET ALL REQUESTS
  async function getAllRequests() {
    setLoading(true)
    const arrOfRequests = []
    const categoriesDocs = await getDocs(collection(db, 'DeleteRequest'))
    categoriesDocs.forEach((request) => {
      arrOfRequests.push({ id: request.id, ...request.data() })
    })

    setRequests(arrOfRequests)
    setLoading(false)
  }

  // UPDATE REQUEST STATUS
  async function updateRequestStatus(request, status) {
    setLoading(true)
    try {
      await updateDoc(doc(db, 'DeleteRequest', request?.id), {
        status: status,
        updatedAt: new Date(),
      })
      toast.success(`Request ${status} successfully.`)
      getAllRequests()
    } catch (error) {
      toast.error('Something went wrong while updating the request.')
      setLoading(false)
    }
  }

  // DELETE THIS USER
  async function deleteThisUser(request) {
    Swal.fire({
      title: 'Delete User Account?',
      text: 'Are you sure to delete this user? He/She would be unable to use the app anymore. This action cannot be undone.',
      icon: 'question',
      confirmButtonColor: '#e55353',
      confirmButtonText: 'Delete User',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        setLoading(true)
        getDoc(doc(db, 'Users', request?.userId))
          .then(async (response) => {
            if (response.exists()) {
              await deleteDoc(doc(db, 'Users', request?.userId))
                .then(() => toast.success('User deleted successfully.'))
                .catch((error) => toast.error('Something went wrong while deleting the user.'))

              // Update request status to completed
              await updateDoc(doc(db, 'DeleteRequest', request?.id), {
                status: 'completed',
                completedAt: new Date(),
              })
              getAllRequests()
            }
          })
          .catch((error) => toast.error('Something went wrong while deleting the user.'))
      }
    })
  }

  // REJECT REQUEST
  async function rejectRequest(request) {
    Swal.fire({
      title: 'Reject Request?',
      text: 'Are you sure you want to reject this deletion request?',
      icon: 'question',
      confirmButtonColor: '#e55353',
      confirmButtonText: 'Reject',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        await updateRequestStatus(request, 'rejected')
      }
    })
  }

  // APPROVE REQUEST
  async function approveRequest(request) {
    Swal.fire({
      title: 'Approve Request?',
      text: 'Are you sure you want to approve this deletion request? You can then proceed to delete the user account.',
      icon: 'question',
      confirmButtonColor: '#28a745',
      confirmButtonText: 'Approve',
      cancelButtonColor: '#9da5b1',
      showCancelButton: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        await updateRequestStatus(request, 'approved')
      }
    })
  }

  // GET STATUS BADGE
  function getStatusBadge(status) {
    switch (status) {
      case 'pending':
        return <CBadge color="warning">Pending</CBadge>
      case 'approved':
        return <CBadge color="success">Approved</CBadge>
      case 'rejected':
        return <CBadge color="danger">Rejected</CBadge>
      case 'completed':
        return <CBadge color="info">Completed</CBadge>
      default:
        return <CBadge color="secondary">Unknown</CBadge>
    }
  }
  useEffect(() => {
    getAllRequests()
  }, [])
  if (isLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )
  return (
    <>
      <h4>Delete Requests</h4>

      {/* TABLE */}
      {requests.length ? (
        <CTable style={{ verticalAlign: 'middle' }}>
          <CTableHead>
            <CTableRow>
              <CTableHeaderCell scope="col">#</CTableHeaderCell>
              <CTableHeaderCell scope="col">Name</CTableHeaderCell>
              <CTableHeaderCell scope="col">Email</CTableHeaderCell>
              <CTableHeaderCell scope="col">Comment</CTableHeaderCell>
              <CTableHeaderCell scope="col">Status</CTableHeaderCell>
              <CTableHeaderCell scope="col">Date</CTableHeaderCell>
              <CTableHeaderCell scope="col">Actions</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            {requests?.map((item, i) => {
              return (
                <CTableRow key={i}>
                  <CTableHeaderCell scope="row">{i + 1}</CTableHeaderCell>
                  <CTableDataCell>
                    {item?.firstName} {item?.surName}
                  </CTableDataCell>
                  <CTableDataCell>{item?.email}</CTableDataCell>
                  <CTableDataCell style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                    {item?.comment}
                  </CTableDataCell>
                  <CTableDataCell>
                    {getStatusBadge(item?.status || 'pending')}
                  </CTableDataCell>
                  <CTableDataCell>
                    {item?.createdAt ? moment(item.createdAt.toDate()).format('MMM DD, YYYY') : 'N/A'}
                  </CTableDataCell>
                  <CTableDataCell className="action-buttons">
                    {item?.status === 'pending' && (
                      <>
                        <CButton
                          color="success"
                          size="sm"
                          className="me-1"
                          onClick={() => approveRequest(item)}
                          title="Approve Request"
                        >
                          <CIcon icon={cilCheckCircle} className="text-light" />
                        </CButton>
                        <CButton
                          color="danger"
                          size="sm"
                          className="me-1"
                          onClick={() => rejectRequest(item)}
                          title="Reject Request"
                        >
                          <CIcon icon={cilXCircle} className="text-light" />
                        </CButton>
                      </>
                    )}
                    {item?.status === 'approved' && (
                      <CButton
                        color="danger"
                        size="sm"
                        className="me-1"
                        onClick={() => deleteThisUser(item)}
                        title="Delete User Account"
                      >
                        <CIcon icon={cilTrash} className="text-light" />
                      </CButton>
                    )}
                    {(item?.status === 'rejected' || item?.status === 'completed') && (
                      <span className="text-muted">No actions available</span>
                    )}
                  </CTableDataCell>
                </CTableRow>
              )
            })}
          </CTableBody>
        </CTable>
      ) : (
        <EmptyBox label={'No requests yet..'} />
      )}
    </>
  )
}

export default Requests
