/* eslint-disable react/prop-types */
import React from "react";
import { C<PERSON><PERSON>, CRow, CCol, CContainer } from "@coreui/react";
import Logo from "../../../assets/images/logo.png";
import support from "../../../assets/images/support.png";

const DelAccountReq = () => {
  document.title = "Support - Glitter of hope";
  return (
    <>
      <div className="vw-100 vh-100 d-flex justify-content-center align-items-center">
        <CContainer>
          <div className="d-flex align-items-center">
            <CImage src={Logo} style={{ width: 100 }} />
          </div>
          <CRow>
            <CCol
              sm={6}
              className="d-flex flex-column justify-content-center align-items-center"
            >
              <div>
                <h1 className="mb-3">To Contact Us ..</h1>
                {/* <h6 className="mb-3"><PERSON></h6> */}
                <p className="mb-3">Email: <EMAIL></p>
                {/* <p>Or call: +1 848 264 2249</p> */}
              </div>
            </CCol>
            <CCol sm={6}>
              <CImage src={support} />
            </CCol>
          </CRow>
        </CContainer>
      </div>
    </>
  );
};

export default DelAccountReq;
