import React, { useState, useEffect, useRef } from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CForm,
  CFormLabel,
  CRow,
  CSpinner,
} from '@coreui/react'
import { addDoc, collection, doc, getDocs, updateDoc, where } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'
import { toast } from 'react-toastify'
import ReactQuill from 'react-quill'

const ContactUS = (props) => {
  const editorRef = useRef()
  const [grandLoading, setGrandLoading] = useState(true)
  const [isLoading, setLoading] = useState(false)
  const [contactUs, setContactUs] = useState(null)
  const [content, setContent] = useState(null)
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, 4, false] }],
      ['bold', 'italic', 'underline', 'strike'], // toggled buttons
      ['blockquote', 'code-block'],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
      [{ color: [] }, { background: [] }], // dropdown with defaults from theme
      [{ align: [] }],
      ['clean'], // remove formatting button
    ],
  }

  // CHANGE HANDLES - TEXT EDITOR
  const handleChange = (value) => {
    setContent(value)
  }

  // GET PRIVACY POLICY
  async function getPrivacyPolicyCotent() {
    setGrandLoading(true)
    await getDocs(collection(db, 'AppData'), where('type', '==', 'contact'))
      .then((response) => {
        response.forEach((doc) => {
          if (doc.data()?.type === 'contact') {
            setContactUs({ id: doc.id, ...doc.data() })
            setContent(doc.data()?.content)
            setGrandLoading(false)
          }
        })
      })
      .catch(() => {
        setContent(null)
        setGrandLoading(false)
      })
  }

  // UPDATE PRIVACY POLICY
  async function updatePrivacyPolicy() {
    setLoading(true)
    if (contactUs?.id) {
      await updateDoc(doc(db, 'AppData', contactUs?.id), { content })
        .then(() => {
          toast.success('Contact Information updated successfully.')
          getPrivacyPolicyCotent()
        })
        .catch(() => toast.success('Something went wrong while updating contact us.'))
      setLoading(false)
      return
    }
    await addDoc(doc(db, 'AppData'), {
      type: 'contact',
      content,
    })
      .then(() => toast.success('Contact Information added successfully.'))
      .catch(() => toast.success('Something went wrong while adding contact us.'))
    setLoading(false)
  }

  useEffect(() => {
    getPrivacyPolicyCotent()
  }, [])

  // LOADING
  if (grandLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Contact Information Details</strong>
          </CCardHeader>
          <CCardBody>
            <CForm>
              <CCol md={12} className="mt-4 mb-4">
                <CFormLabel htmlFor="inputZip">Enter/Update Contact Information</CFormLabel>
                <ReactQuill
                  ref={editorRef}
                  theme="snow"
                  modules={modules}
                  value={content}
                  onChange={handleChange}
                />
              </CCol>

              <CButton
                type="submit"
                color="primary"
                disabled={isLoading}
                onClick={updatePrivacyPolicy}
              >
                Save
                {isLoading ? <CSpinner size="sm" className="ms-1" /> : null}
              </CButton>
            </CForm>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  )
}

export default ContactUS
