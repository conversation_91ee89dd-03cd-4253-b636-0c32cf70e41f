/* eslint-disable react/prop-types */
import { CButton, CContainer, CForm, CFormInput, CSpinner } from '@coreui/react'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { joiResolver } from '@hookform/resolvers/joi'
import Joi from 'joi'
import { auth } from 'src/firebase/firebase'
import { toast } from 'react-toastify'
import { signInWithEmailAndPassword, updatePassword } from 'firebase/auth'

const oldPasswordSchema = Joi.object({
  oldPassword: Joi.string().empty().required().messages({
    'string.base': 'Old Password is required.',
    'string.required': 'Old Password is required.',
    'string.empty': 'Old Password is required.',
  }),
})

const newPasswordSchema = Joi.object({
  newPassword: Joi.string().empty().required().min(6).required().messages({
    'string.base': 'New Password is required.',
    'string.required': 'New Password is required.',
    'string.empty': 'New Password is required.',
    'string.min': 'Password must have atleast 6 letters.',
  }),
  confirmPassword: Joi.valid(Joi.ref('newPassword')).required().messages({
    'string.base': 'Confirm Password is required.',
    'string.required': 'Confirm Password is required.',
    'string.empty': 'Confirm Password is required.',
    'string.min': 'Password must have atleast 6 letters.',
    'any.only': 'Password did not match.',
  }),
})

const Profile = () => {
  const user = JSON.parse(localStorage.getItem('userData'))
  const userId = localStorage.getItem('userId')
  const [isValidated, setValidated] = useState(false)
  const [validatingLoading, setValidatingLoading] = useState(false)
  const [passwordLoading, setPasswordLoading] = useState(false)

  const {
    handleSubmit,
    register,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      oldPassword: '',
    },
    mode: 'all',
    resolver: joiResolver(oldPasswordSchema),
  })
  const {
    register: register1,
    handleSubmit: handleSubmit1,
    formState: { errors: errors1 },
    reset: reset1,
  } = useForm({
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
    },
    mode: 'all',
    resolver: joiResolver(newPasswordSchema),
  })

  async function validateLoggedInUser(formData) {
    setValidatingLoading(true)
    await signInWithEmailAndPassword(auth, user?.email, formData.oldPassword)
      .then((response) => {
        setValidated(true)
        reset()
      })
      .catch((error) => {
        // INVAVLID CREDENTIALS
        if (error.code === 'auth/wrong-password') {
          toast.error('Invalid email or password')
        }
      })
    setValidatingLoading(false)
  }

  // SET NEW PASSWORD
  async function updateUserPassword(formData) {
    setPasswordLoading(true)
    await updatePassword(auth.currentUser, formData?.newPassword)
      .then(() => {
        toast.success('Password updated successfully.')
        setPasswordLoading(false)
        setValidated(false)
        reset1()
      })
      .catch((error) => {
        setPasswordLoading(false)
        toast.error('Something went wrong while update password.')
      })
  }

  return (
    <>
      <h4>Profile</h4>
      <CContainer>
        {/* VALIDATE OLD PASSWORD */}
        {!isValidated ? (
          <CForm onSubmit={handleSubmit(validateLoggedInUser)}>
            <div className="mb-3">
              <label htmlFor="oldPassword">Old Password</label>
              <CFormInput
                id="oldPassword"
                type="password"
                {...register('oldPassword')}
                disabled={isValidated}
              />
              {errors?.oldPassword ? (
                <p className="text-danger">{errors?.oldPassword?.message}</p>
              ) : null}
            </div>
            <div className="mb-3 text-end">
              <CButton disabled={validatingLoading || isValidated} type="submit">
                Submit
                {validatingLoading ? <CSpinner size="sm" className="ms-2" /> : null}
              </CButton>
            </div>
          </CForm>
        ) : null}

        {/* SET NEW PASSWORD */}
        {isValidated ? (
          <CForm onSubmit={handleSubmit1(updateUserPassword)}>
            <div className="mb-3">
              <label htmlFor="newPassword">New Password</label>
              <CFormInput id="newPassword" type="password" {...register1('newPassword')} />
              {errors1?.newPassword ? (
                <p className="text-danger">{errors1?.newPassword?.message}</p>
              ) : null}
            </div>
            <div className="mb-3">
              <label htmlFor="confirmPassword">Confirm New Password</label>
              <CFormInput id="confirmPassword" type="password" {...register1('confirmPassword')} />
              {errors1?.confirmPassword ? (
                <p className="text-danger">{errors1?.confirmPassword?.message}</p>
              ) : null}
            </div>
            <div className="mb-3 text-end">
              <CButton disabled={passwordLoading} type="submit">
                Submit
                {passwordLoading ? <CSpinner size="sm" className="ms-2" /> : null}
              </CButton>
            </div>
          </CForm>
        ) : null}
      </CContainer>
    </>
  )
}

export default Profile
