/* eslint-disable react/prop-types */

import React, { useState } from 'react'
import {
  CButton,
  CForm,
  CFormInput,
  CFormLabel,
  CModal,
  CModalBody,
  CModalHeader,
  CSpinner,
} from '@coreui/react'
import { useForm } from 'react-hook-form'
import { joiResolver } from '@hookform/resolvers/joi'
import { adSchema } from './ad.schema'
import { uploadThisImage } from 'src/utils/FirebaseUtils'
import { addDoc, collection } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'
import { toast } from 'react-toastify'

const AdForm = ({ isVisible, closeModal, ad }) => {
  const [isLoading, setLoading] = useState(false)

  const {
    handleSubmit,
    register,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: '',
      link: '',
      image: '',
    },
    resolver: joiResolver(adSchema),
  })
  const formData = watch()

  // CLOSE MODAL
  const onCloseModal = () => {
    closeModal()
    reset()
  }

  // HANDLE CHANGE IMAGE OF FORM
  const handleChangeImage = async (event) => {
    const file = event?.target?.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const dataUrl = e.target.result
        setValue('image', dataUrl)
      }
      reader.readAsDataURL(file)
    }
  }

  async function submitFormToCreateAd(formData) {
    setLoading(true)
    const { title, link, image } = formData
    try {
      // CREATE NEW AD
      if (!ad) {
        const urlOfUploadedImage = await uploadThisImage(image, 'Categories')
        await addDoc(collection(db, 'Ads'), { title, link, image: urlOfUploadedImage })
        toast.success('New ad created successfully.')
      }

      onCloseModal()
    } catch (error) {
      toast.error('Something went wrong.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <CModal visible={isVisible} onClose={onCloseModal} size="lg">
        <CModalHeader>
          <h5 className="m-0">{ad ? 'Edit Ad' : 'Create Add'}</h5>
        </CModalHeader>
        <CModalBody>
          <CForm onSubmit={handleSubmit(submitFormToCreateAd)}>
            {/* TITLE */}
            <div className="mb-3">
              <CFormLabel htmlFor="title">Title</CFormLabel>
              <CFormInput type="text" id="title" {...register('title')} />
              {errors?.title ? <p className="text-danger">{errors?.title.message}</p> : null}
            </div>

            {/* LINK */}
            <div className="mb-3">
              <CFormLabel htmlFor="link">Website Link</CFormLabel>
              <CFormInput type="text" id="link" {...register('link')} />
              {errors?.link ? <p className="text-danger">{errors?.link.message}</p> : null}
            </div>

            {/* IMAGE */}
            <div className="mb-3">
              <CFormLabel htmlFor="image">Image</CFormLabel>
              <CFormInput
                type="file"
                id="image"
                accept="image/png, image/jpg, image/jpeg"
                onChange={(event) => handleChangeImage(event)}
              />
              {errors?.image ? <p className="text-danger">{errors?.image.message}</p> : null}
            </div>

            {formData?.image ? (
              <div className="d-flex mb-3">
                <img
                  src={formData?.image}
                  className="rounded border mt-2 mx-auto"
                  style={{ width: 'auto', maxHeight: '10rem', objectFit: 'contain' }}
                />
              </div>
            ) : null}

            {/* SUMIT BUTTON */}
            <div className="text-end">
              <CButton type="submit" className="px-4" disabled={isLoading}>
                {isLoading ? <CSpinner size="sm" /> : 'Save'}
              </CButton>
            </div>
          </CForm>
        </CModalBody>
      </CModal>
    </>
  )
}

export default AdForm
