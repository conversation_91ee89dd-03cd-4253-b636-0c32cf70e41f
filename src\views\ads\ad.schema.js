import Joi from 'joi'

const adSchema = Joi.object({
  title: Joi.string().required().messages({
    'string.empty': 'Title is required.',
    'string.required': 'Title is required.',
  }),
  link: Joi.string().required().messages({
    'string.empty': 'Website Link is required.',
    'string.required': 'Website Link is required.',
  }),
  image: Joi.string().required().messages({
    'string.empty': 'Image is required.',
    'string.required': 'Image is required.',
  }),
})

export { adSchema }
