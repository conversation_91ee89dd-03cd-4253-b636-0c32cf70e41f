/* eslint-disable react/prop-types */
import React, { useState, useEffect, useMemo } from "react";
import {
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CSpinner,
  CContainer,
  CImage,
  CFormInput,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
  CFormSelect,
} from "@coreui/react";
import {
  getDocs,
  collection,
  updateDoc,
  doc,
  getDoc,
  deleteDoc,
  query,
  where,
} from "firebase/firestore";
import { db } from "src/firebase/firebase";
import EmptyBox from "src/components/EmptyBox";
import { toast } from "react-toastify";
import { usePagination, useTable, useSortBy } from "react-table";
import profilePlaceholder from "../../../assets/images/profilePlaceholder.png";
import { FaEye } from "react-icons/fa";
import { FcApprove } from "react-icons/fc";
import moment from "moment";
import { ImBlocked } from "react-icons/im";

const Users = () => {
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);

  const [isLoading, setLoading] = useState(true);
  const [refresh, setRefresh] = useState(0);
  const [searchString, setSearchString] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [modalData, setModalData] = useState(null);
  const [pets, setPets] = useState([]);
  const [statusFilter, setStatusFilter] = useState("");

  const handleSearchString = (str) => setSearchString(str);
  const handleStatusFilter = (status) => setStatusFilter(status);

  // TABLE COLUMNS
  const columns = useMemo(
    () => [
      {
        Header: "#",
        Cell: ({ row }) => {
          return <span>{row.index + 1}</span>;
        },
      },
      {
        Header: "Image",
        accessor: "profilePicture",
        Cell: (row) => {
          return (
            <CImage
              width={40}
              height={40}
              className=" object-contain"
              src={row?.value || profilePlaceholder}
              style={{ borderRadius: 5 }}
            />
          );
        },
      },
      {
        Header: "Name",
        accessor: "name",
        Cell: ({ row }) => {
          const { name } = row.original;
          return <span>{name}</span>;
        },
      },
      { Header: "Email", accessor: "email" },
      {
        Header: "Actions",
        Cell: ({ row }) => {
          return (
            <div className="d-flex justify-content-start">
              {row.original.profileStatus === "pending" && (
                <CButton
                  size="sm"
                  color="success text-white"
                  onClick={() => updateActiveStatus(row.original, true)}
                >
                  Approve <FcApprove />
                </CButton>
              )}
              <CButton
                size="sm"
                color={row.original.isBlocked ? "success" : "danger"}
                text-white
                className="ms-2 text-light"
                onClick={() => toggleBlockUser(row.original)}
              >
                {row.original.isBlocked ? "Unblock" : "Block"} <ImBlocked />
              </CButton>
              <CButton
                size="sm"
                color="info text-white ms-2"
                onClick={() => openModal(row.original)}
              >
                Show <FaEye />
              </CButton>
            </div>
          );
        },
      },
    ],
    []
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    page,
    prepareRow,
    canPreviousPage,
    pageCount,
    gotoPage,
    canNextPage,
    nextPage,
    previousPage,
    pageOptions,
    state: { pageIndex },
  } = useTable(
    {
      columns,
      data: filteredUsers,
      initialState: { pageIndex: 0 },
    },
    useSortBy,
    usePagination
  );

  // GET ALL USERS
  async function getAllUsers() {
    let q = query(collection(db, "Users"));
    setLoading(true);
    await getDocs(q).then((response) => {
      const arrOfUsers = [];
      response.docs.forEach((user) => {
        const userData = user.data();
        if (!userData.isAdmin) {
          arrOfUsers.push({ id: user.id, ...userData });
        }
      });
      setUsers(arrOfUsers);
      setFilteredUsers(arrOfUsers);
    });
    setLoading(false);
  }

  // UPDATE ACTIVE STATE
  async function updateActiveStatus(user, flag) {
    setLoading(true);
    await updateDoc(doc(db, "Users", user?.id), { profileStatus: "approved" })
      .then(async () => {
        setRefresh((n) => n + 1);
      })
      .catch((error) => {
        setLoading(false);
        toast.error("Something went wrong while updating the user status.");
      });
  }
  async function toggleBlockUser(user) {
    setLoading(true);
    await updateDoc(doc(db, "Users", user?.id), { isBlocked: !user.isBlocked })
      .then(async () => {
        setRefresh((n) => n + 1);
        toast.success(
          `User ${user.isBlocked ? "unblocked" : "blocked"} successfully.`
        );
      })
      .catch((error) => {
        setLoading(false);
        toast.error("Something went wrong while updating the user status.");
      });
  }

  // DELETE THIS USER

  // OPEN MODAL
  function openModal(user) {
    setModalData(user);
    setShowModal(true);
    getUserPets(user.id);
  }

  // CLOSE MODAL
  function closeModal() {
    setShowModal(false);
    setModalData(null);
    setPets([]);
  }
  async function getUserPets(userId) {
    const q = query(collection(db, "Pets"), where("userId", "==", userId));
    const querySnapshot = await getDocs(q);
    const petsArray = [];
    querySnapshot.forEach((doc) => {
      petsArray.push({ id: doc.id, ...doc.data() });
    });
    setPets(petsArray);
  }

  useEffect(() => {
    getAllUsers();
  }, [refresh]);

  // SEARCH ORDER WITH ID
  function handleFilterUsers() {
    let filteredArr = users;
    if (searchString) {
      filteredArr = filteredArr.filter((item) => {
        if (
          item?.name?.toLowerCase()?.includes(searchString?.toLowerCase()) ||
          item?.surName?.toLowerCase()?.includes(searchString?.toLowerCase()) ||
          item?.email?.includes(searchString?.toLowerCase())
        ) {
          return item;
        }
        return null;
      });
    }
    if (statusFilter) {
      filteredArr = filteredArr.filter(
        (item) => item.profileStatus === statusFilter
      );
    }
    setFilteredUsers(filteredArr);
  }

  useEffect(() => {
    handleFilterUsers();
  }, [searchString, statusFilter, users]);

  // LOADING
  if (isLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    );
  return (
    <CContainer>
      <h4>Users</h4>

      {/* SEARCH INPUT FIELD */}
      <div className="d-flex my-3">
        <CFormInput
          placeholder="Search"
          className="me-3"
          type="search"
          value={searchString}
          onChange={(e) => handleSearchString(e.target.value)}
        />
        <CFormSelect
          className="ml-2"
          value={statusFilter}
          onChange={(e) => handleStatusFilter(e.target.value)}
        >
          <option value="">All</option>
          <option value="approved">Approved</option>
          <option value="pending">Pending</option>
        </CFormSelect>
      </div>
      {/* USERS TABLE */}
      {filteredUsers.length ? (
        <>
          <CTable {...getTableProps()} className="table" responsive>
            <CTableHead>
              {headerGroups.map((headerGroup, i) => {
                return (
                  <CTableRow key={i} {...headerGroup.getHeaderGroupProps()}>
                    {headerGroup.headers.map((column, i) => {
                      return (
                        <CTableHeaderCell key={i} {...column.getHeaderProps()}>
                          {column.render("Header")}
                        </CTableHeaderCell>
                      );
                    })}
                  </CTableRow>
                );
              })}
            </CTableHead>
            <CTableBody {...getTableBodyProps()}>
              {page.map((row, i) => {
                prepareRow(row);
                return (
                  <CTableRow key={i} {...row.getRowProps()}>
                    {row.cells.map((cell, i) => {
                      return (
                        <CTableDataCell key={i} {...cell.getCellProps()}>
                          {cell.render("Cell")}
                        </CTableDataCell>
                      );
                    })}
                  </CTableRow>
                );
              })}
            </CTableBody>
          </CTable>

          {/* PAGINATION */}
          <div className="d-flex justify-content-center gap-4">
            <button
              onClick={() => gotoPage(0)}
              disabled={!canPreviousPage}
              className="btn btn-secondary"
            >
              {"<<"}
            </button>
            <button
              onClick={() => previousPage()}
              disabled={!canPreviousPage}
              className="btn btn-secondary"
            >
              {"<"}
            </button>
            <button
              onClick={() => nextPage()}
              disabled={!canNextPage}
              className="btn btn-secondary"
            >
              {">"}
            </button>
            <button
              onClick={() => gotoPage(pageCount - 1)}
              disabled={!canNextPage}
              className="btn btn-secondary"
            >
              {">>"}
            </button>
          </div>
        </>
      ) : (
        <EmptyBox
          heading="No Users Found"
          description="No users available. Please add users to see them here."
        />
      )}

      {/* MODAL */}
      <CModal visible={showModal} onClose={closeModal}>
        <CModalHeader>
          <CModalTitle>User Details</CModalTitle>
        </CModalHeader>
        <CModalBody>
          {modalData && (
            <>
              <div className="text-center">
                <CImage
                  width={100}
                  height={100}
                  className=" object-cover"
                  src={modalData.profilePicture || profilePlaceholder}
                  style={{ borderRadius: 50 }}
                />
              </div>
              <div className="mt-3">
                <strong>Name: </strong> {modalData.name}
              </div>
              <div className="mt-1">
                <strong>Email: </strong> {modalData.email}
              </div>
              <div className="mt-1">
                <strong>Phone: </strong> {modalData.phoneNumber || ""}
              </div>
            </>
          )}
        </CModalBody>
        <CModalFooter>
          {modalData?.profileStatus === "pending" && (
            <CButton
              color="success text-white"
              onClick={() => updateActiveStatus(modalData)}
            >
              Approve <FcApprove />
            </CButton>
          )}
          <CButton
            color={modalData?.isBlocked ? "success" : "danger"}
            text-white
            className="ms-2 text-light"
            onClick={() => toggleBlockUser(modalData)}
          >
            {modalData?.isBlocked ? "Unblock" : "Block"} <ImBlocked />
          </CButton>
          <CButton color="secondary" onClick={closeModal}>
            Close
          </CButton>
        </CModalFooter>
      </CModal>
    </CContainer>
  );
};

export default Users;
