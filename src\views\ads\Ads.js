import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>on, <PERSON><PERSON>, <PERSON>ow, <PERSON>pinner } from '@coreui/react'
import AdForm from './AdForm'
import CIcon from '@coreui/icons-react'
import { cilTrash } from '@coreui/icons'
import { collection, getDocs } from 'firebase/firestore'
import { db } from 'src/firebase/firebase'
import AdCard from 'src/components/AdCard/AdCard'

const Ads = () => {
  const [ads, setAds] = useState([])
  const [isLoading, setLoading] = useState(true)

  // MODAL HANLDERS - CREATE AD
  const [isCreateAddVisible, setCreateAddVisible] = useState(false)
  const handleShowCreateAd = () => setCreateAddVisible(true)
  const handleCloseCreateAd = () => setCreateAddVisible(false)

  // VIEW DETAILS
  const [adDetails, setAdDetails] = useState(null)
  const [isDetailsVisible, setDetailsAddVisible] = useState(false)
  const handleShowDetails = () => setDetailsAddVisible(true)
  const handleCloseDetails = () => setDetailsAddVisible(false)

  // GET ALL CATEGORIES
  async function getAllAds() {
    setLoading(true)
    const arrOfAds = []
    const adsDocs = await getDocs(collection(db, 'Ads'))
    adsDocs.forEach((ad) => {
      arrOfAds.push({ id: ad.id, ...ad.data() })
    })

    setAds(arrOfAds)
    setLoading(false)
  }

  useEffect(() => {
    getAllAds()
  }, [])

  if (isLoading)
    return (
      <div className="d-flex justify-content-center align-items-center">
        <CSpinner />
      </div>
    )

  return (
    <>
      <div className="d-flex justify-content-between mb-2">
        <h4>Ads</h4>
        <CButton onClick={handleShowCreateAd} color="primary" className="text-light" size="sm">
          Create Add
        </CButton>
      </div>

      {ads.length ? (
        <>
          <CRow>
            {ads.map((item) => {
              return (
                <CCol md={4} sm={4} xs={6} key={item.id}>
                  <AdCard ad={item} />
                </CCol>
              )
            })}
          </CRow>
        </>
      ) : null}

      {/* AD FORM */}
      <AdForm isVisible={isCreateAddVisible} closeModal={handleCloseCreateAd} />
    </>
  )
}

export default Ads
