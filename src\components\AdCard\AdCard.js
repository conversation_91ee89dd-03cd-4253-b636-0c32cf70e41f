/* eslint-disable react/prop-types */
import React from 'react'
import { CButton, CCard, CCardBody, CCardImage, CCardTitle } from '@coreui/react'

const AdCard = ({ ad, onViewDetails }) => {
  const imageStyles = { height: '10rem', objectFit: 'cover' }

  return (
    <>
      <CCard>
        <CCardImage orientation="top" src={ad?.image} style={imageStyles} />
        <CCardBody>
          <div className="d-flex justify-content-between">
            <CCardTitle className="text-truncate mb-3" title={ad?.title}>
              {ad?.title}
            </CCardTitle>
          </div>
          <a
            href={ad.link}
            target="_blank"
            rel="noopener noreferrer"
            className="btn btn-small btn-outline-secondary w-100 mb-3 text-dark text-center"
          >
            Open Link
          </a>
          {/* <CCardText className="text-truncate mb-1">{category?.description}</CCardText> */}
          <CButton className="w-100" onClick={onViewDetails}>
            View Details
          </CButton>
        </CCardBody>
      </CCard>
    </>
  )
}

export default AdCard
